# Account Management Integration Test Checklist

## Overview
This checklist ensures the Next.js admin frontend integration with Django backend APIs is working correctly for account management functionality.

## Prerequisites
- [ ] Django backend server is running on `http://localhost:8000`
- [ ] Database is properly set up with RLS policies
- [ ] Test users exist with different roles (admin, manager, staff, customer)
- [ ] Next.js admin frontend is running on `http://localhost:3000`

## Test Users Setup
Create these test users in the Django admin or through API:

```
Admin User:
- Email: <EMAIL>
- Password: admin123
- Role: admin

Manager User:
- Email: <EMAIL>
- Password: manager123
- Role: manager

Staff User:
- Email: <EMAIL>
- Password: staff123
- Role: staff

Customer User:
- Email: <EMAIL>
- Password: customer123
- Role: customer
```

## 1. Authentication Tests

### 1.1 Admin Login
- [ ] Navigate to admin login page
- [ ] Login with admin credentials
- [ ] Verify successful authentication
- [ ] Check user role is displayed correctly
- [ ] Verify JWT token is stored in localStorage

### 1.2 Role-Based Access
- [ ] Test login with each user role
- [ ] Verify appropriate dashboard access
- [ ] Check that customers cannot access admin dashboard

## 2. Account Listing Tests

### 2.1 Account List Display
- [ ] Navigate to accounts page
- [ ] Verify accounts are loaded from Django API
- [ ] Check account data displays correctly (number, type, balance, status)
- [ ] Test pagination if applicable
- [ ] Test search/filter functionality

### 2.2 Role-Based Account Visibility
- [ ] Login as admin - should see all accounts
- [ ] Login as manager - should see appropriate accounts
- [ ] Login as staff - should see limited account data
- [ ] Verify RLS policies are enforced

## 3. Edit Account Dialog Tests

### 3.1 Basic Functionality
- [ ] Click edit button on an account
- [ ] Verify dialog opens with current account data
- [ ] Check all form fields are populated correctly
- [ ] Test form validation

### 3.2 Account Type Editing
- [ ] Login as admin - should be able to change account type
- [ ] Login as manager - account type should be disabled
- [ ] Login as staff - account type should be disabled
- [ ] Verify permission message is shown

### 3.3 Balance Editing
- [ ] Login as admin - should be able to edit balance
- [ ] Login as manager - should be able to edit balance
- [ ] Login as staff - balance should be disabled
- [ ] Verify permission message is shown

### 3.4 Status Editing
- [ ] Login as admin - should see all status options including "Closed"
- [ ] Login as manager - should see all status options including "Closed"
- [ ] Login as staff - should see limited status options (no "Closed")
- [ ] Test status change functionality

### 3.5 API Integration
- [ ] Make valid changes and submit
- [ ] Verify success message is shown
- [ ] Check account is updated in database
- [ ] Test with invalid data - verify error handling
- [ ] Test network error scenarios

## 4. Create Account Dialog Tests

### 4.1 Access Control
- [ ] Login as customer - should show "Access Denied" dialog
- [ ] Login as staff - should have access to create dialog
- [ ] Login as manager - should have full access
- [ ] Login as admin - should have full access

### 4.2 Form Fields
- [ ] Verify all required fields are marked with *
- [ ] Test form validation for required fields
- [ ] Check email validation
- [ ] Test phone number formatting
- [ ] Verify initial deposit accepts decimal values

### 4.3 Account Type Restrictions
- [ ] Login as staff - should not see "Business Account" option
- [ ] Login as manager - should see all account types
- [ ] Login as admin - should see all account types
- [ ] Verify permission message for business accounts

### 4.4 Account Creation
- [ ] Fill valid customer information
- [ ] Select account type (savings/checking)
- [ ] Add initial deposit
- [ ] Submit form
- [ ] Verify success message
- [ ] Check new account appears in account list
- [ ] Verify account number is auto-generated

### 4.5 User Creation/Lookup
- [ ] Create account for existing user (by email)
- [ ] Create account for new user
- [ ] Verify user is created if doesn't exist
- [ ] Test duplicate email handling

## 5. Error Handling Tests

### 5.1 Network Errors
- [ ] Disconnect from internet
- [ ] Try to edit/create account
- [ ] Verify appropriate error message
- [ ] Test retry functionality

### 5.2 Authentication Errors
- [ ] Let JWT token expire
- [ ] Try to perform account operation
- [ ] Verify token refresh works
- [ ] Test logout on authentication failure

### 5.3 Validation Errors
- [ ] Submit form with invalid data
- [ ] Verify field-specific error messages
- [ ] Test server-side validation errors
- [ ] Check error message formatting

### 5.4 Permission Errors
- [ ] Try to perform action without permission
- [ ] Verify 403 error is handled gracefully
- [ ] Check appropriate error message is shown

## 6. Loading States Tests

### 6.1 Form Submission
- [ ] Submit edit account form
- [ ] Verify loading spinner appears
- [ ] Check submit button is disabled during loading
- [ ] Verify loading state clears on completion

### 6.2 Account Creation
- [ ] Submit create account form
- [ ] Verify loading indicator
- [ ] Check form is disabled during submission
- [ ] Test loading state on error

## 7. Integration Tests

### 7.1 End-to-End Workflow
- [ ] Login as admin
- [ ] Create new account
- [ ] Edit the created account
- [ ] Change account status
- [ ] Verify all changes persist
- [ ] Logout and login as different role
- [ ] Verify role-based restrictions

### 7.2 Data Consistency
- [ ] Create account in admin frontend
- [ ] Verify account appears in Django admin
- [ ] Check database directly
- [ ] Verify RLS policies are working

## 8. Performance Tests

### 8.1 Response Times
- [ ] Measure account list loading time
- [ ] Test edit dialog opening speed
- [ ] Check form submission response time
- [ ] Verify acceptable performance under load

## 9. Browser Compatibility

### 9.1 Cross-Browser Testing
- [ ] Test in Chrome
- [ ] Test in Firefox
- [ ] Test in Safari
- [ ] Test in Edge
- [ ] Verify consistent behavior

## 10. Mobile Responsiveness

### 10.1 Mobile Layout
- [ ] Test on mobile device/emulator
- [ ] Verify dialogs are responsive
- [ ] Check form usability on small screens
- [ ] Test touch interactions

## Test Results

### Summary
- Total Tests: ___
- Passed: ___
- Failed: ___
- Success Rate: ___%

### Failed Tests
List any failed tests and their issues:

1. 
2. 
3. 

### Notes
Additional observations or issues found during testing:

---

**Test Completed By:** _______________
**Date:** _______________
**Environment:** _______________
