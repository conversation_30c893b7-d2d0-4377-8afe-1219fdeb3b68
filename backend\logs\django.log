ERROR 2025-07-15 13:31:10,903 supabase_client 23340 13772 Failed to initialize Supabase client: Invalid API key
INFO 2025-07-15 13:32:53,535 supabase_client 19608 25448 Using development mode - Supabase client not initialized
INFO 2025-07-15 13:34:11,482 supabase_client 1008 21120 Using development mode - Supabase client not initialized
INFO 2025-07-15 13:35:14,203 supabase_client 23136 7736 Using development mode - Supabase client not initialized
INFO 2025-07-15 13:36:49,153 supabase_client 23076 19256 Using development mode - Supabase client not initialized
INFO 2025-07-15 13:38:15,664 supabase_client 12444 1144 Using development mode - Supabase client not initialized
ERROR 2025-07-15 13:38:15,785 log 12444 1144 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\http\request.py", line 202, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
ERROR 2025-07-15 13:38:16,277 log 12444 1144 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\http\request.py", line 202, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
INFO 2025-07-15 13:39:05,955 supabase_client 2680 18012 Using development mode - Supabase client not initialized
ERROR 2025-07-15 13:43:26,237 utils 2680 18012 Failed to create audit log: connection timeout expired
Multiple connection attempts failed. All failures were:
- host: 'localhost', port: '5432', hostaddr: '::1': connection timeout expired
- host: 'localhost', port: '5432', hostaddr: '127.0.0.1': connection timeout expired
WARNING 2025-07-15 13:43:26,485 log 2680 18012 Bad Request: /api/auth/login/
WARNING 2025-07-15 13:43:26,542 log 2680 18012 Bad Request: /api/auth/token/refresh/
INFO 2025-07-16 12:44:44,228 autoreload 2988 18560 Watching for file changes with StatReloader
INFO 2025-07-16 12:47:41,847 autoreload 27156 30760 Watching for file changes with StatReloader
INFO 2025-07-16 12:47:46,297 supabase_client 27156 30760 Supabase client initialized successfully for exobank project
INFO 2025-07-16 13:10:23,059 autoreload 31500 28272 Watching for file changes with StatReloader
INFO 2025-07-16 13:10:26,602 supabase_client 31500 28272 Supabase client initialized successfully for exobank project
INFO 2025-07-16 13:12:53,448 autoreload 12472 29544 Watching for file changes with StatReloader
INFO 2025-07-16 13:12:56,986 supabase_client 12472 29544 Supabase client initialized successfully for exobank project
INFO 2025-07-16 13:16:05,233 autoreload 26724 29896 Watching for file changes with StatReloader
INFO 2025-07-16 13:16:08,457 supabase_client 26724 29896 Supabase client initialized successfully for exobank project
INFO 2025-07-16 14:20:28,114 supabase_client 8988 1104 Supabase client initialized successfully for exobank project
INFO 2025-07-16 15:18:59,275 supabase_client 4384 9372 Supabase client initialized successfully for exobank project
INFO 2025-07-16 15:21:44,091 supabase_client 9296 17556 Supabase client initialized successfully for exobank project
INFO 2025-07-16 15:38:13,935 supabase_client 16880 14384 Supabase client initialized successfully for exobank project
INFO 2025-07-16 15:39:25,154 supabase_client 4000 18184 Supabase client initialized successfully for exobank project
INFO 2025-07-16 15:40:39,118 supabase_client 14060 8496 Supabase client initialized successfully for exobank project
INFO 2025-07-16 15:41:10,593 supabase_client 4616 14072 Supabase client initialized successfully for exobank project
INFO 2025-07-16 15:42:31,930 supabase_client 17648 3860 Supabase client initialized successfully for exobank project
INFO 2025-07-16 15:43:27,654 supabase_client 9760 17960 Supabase client initialized successfully for exobank project
INFO 2025-07-16 15:44:00,457 supabase_client 16588 4896 Supabase client initialized successfully for exobank project
INFO 2025-07-16 15:45:15,344 supabase_client 5420 15040 Supabase client initialized successfully for exobank project
INFO 2025-07-16 17:37:26,258 supabase_client 16912 4060 Supabase client initialized successfully for exobank project
INFO 2025-07-16 17:50:43,928 supabase_client 3496 18660 Supabase client initialized successfully for exobank project
INFO 2025-07-16 17:53:45,663 supabase_client 18972 17292 Supabase client initialized successfully for exobank project
INFO 2025-07-16 19:28:51,494 supabase_client 23088 4952 Supabase client initialized successfully for exobank project
INFO 2025-07-16 21:40:14,784 supabase_client 18388 11108 Supabase client initialized successfully for exobank project
INFO 2025-07-17 09:56:19,341 supabase_client 20188 3984 Supabase client initialized successfully for exobank project
INFO 2025-07-17 09:57:09,428 supabase_client 12204 18352 Supabase client initialized successfully for exobank project
INFO 2025-07-17 09:57:37,775 supabase_client 17232 4260 Supabase client initialized successfully for exobank project
INFO 2025-07-17 09:58:17,030 supabase_client 17172 5872 Supabase client initialized successfully for exobank project
INFO 2025-07-17 10:00:28,373 supabase_client 15684 17804 Supabase client initialized successfully for exobank project
INFO 2025-07-17 10:01:38,243 supabase_client 15592 4240 Supabase client initialized successfully for exobank project
INFO 2025-07-17 10:02:23,210 supabase_client 20252 19284 Supabase client initialized successfully for exobank project
INFO 2025-07-17 20:24:36,340 supabase_client 26876 15164 Supabase client initialized successfully for exobank project
INFO 2025-07-18 05:12:17,473 supabase_client 28680 21664 Supabase client initialized successfully for exobank project
ERROR 2025-07-18 05:12:18,529 utils 28680 21664 Failed to create audit log: connection to server at "db.wmlsydbfwhevuaavjkje.supabase.co" (2406:da1a:6b0:f60c:7479:fc44:933a:100), port 5432 failed: FATAL:  password authentication failed for user "postgres.wmlsydbfwhevuaavjkje"

WARNING 2025-07-18 05:12:18,529 middleware 28680 21664 Slow request: GET /api/accounts/ took 4.16s to process
INFO 2025-07-18 05:16:00,499 supabase_client 20504 23040 Supabase client initialized successfully for exobank project
ERROR 2025-07-18 05:16:02,886 utils 20504 23040 Failed to create audit log: connection to server at "db.wmlsydbfwhevuaavjkje.supabase.co" (2406:da1a:6b0:f60c:7479:fc44:933a:100), port 5432 failed: FATAL:  password authentication failed for user "postgres.wmlsydbfwhevuaavjkje"

WARNING 2025-07-18 05:16:02,888 middleware 20504 23040 Slow request: GET /api/accounts/ took 7.67s to process
INFO 2025-07-18 05:16:03,417 _client 20504 23040 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-18 05:16:03,422 supabase_client 20504 23040 Error authenticating user: Invalid API key
WARNING 2025-07-18 05:16:03,428 authentication 20504 23040 Supabase authentication <NAME_EMAIL>
ERROR 2025-07-18 05:16:04,109 views 20504 23040 Login error <NAME_EMAIL>: connection to server at "db.wmlsydbfwhevuaavjkje.supabase.co" (2406:da1a:6b0:f60c:7479:fc44:933a:100), port 5432 failed: FATAL:  password authentication failed for user "postgres.wmlsydbfwhevuaavjkje"

ERROR 2025-07-18 05:16:04,972 utils 20504 23040 Failed to create audit log: connection to server at "db.wmlsydbfwhevuaavjkje.supabase.co" (2406:da1a:6b0:f60c:7479:fc44:933a:100), port 5432 failed: FATAL:  password authentication failed for user "postgres.wmlsydbfwhevuaavjkje"

WARNING 2025-07-18 05:16:04,974 middleware 20504 23040 Slow request: POST /api/auth/login/ took 2.07s to process
ERROR 2025-07-18 05:16:06,088 utils 20504 23040 Failed to create audit log: connection to server at "db.wmlsydbfwhevuaavjkje.supabase.co" (2406:da1a:6b0:f60c:7479:fc44:933a:100), port 5432 failed: FATAL:  password authentication failed for user "postgres.wmlsydbfwhevuaavjkje"

WARNING 2025-07-18 05:16:06,089 middleware 20504 23040 Slow request: GET /api/accounts/ took 1.10s to process
ERROR 2025-07-18 05:16:06,936 utils 20504 23040 Failed to create audit log: connection to server at "db.wmlsydbfwhevuaavjkje.supabase.co" (2406:da1a:6b0:f60c:7479:fc44:933a:100), port 5432 failed: FATAL:  password authentication failed for user "postgres.wmlsydbfwhevuaavjkje"

INFO 2025-07-18 05:19:10,278 supabase_client 18332 31240 Supabase client initialized successfully for exobank project
ERROR 2025-07-18 05:19:31,896 utils 18332 31240 Failed to create audit log: connection to server at "db.wmlsydbfwhevuaavjkje.supabase.co" (2406:da1a:6b0:f60c:7479:fc44:933a:100), port 5432 failed: Connection timed out (0x0000274C/10060)
	Is the server running on that host and accepting TCP/IP connections?

WARNING 2025-07-18 05:19:32,056 middleware 18332 31240 Slow request: GET /api/accounts/ took 30.75s to process
INFO 2025-07-18 05:19:35,089 _client 18332 31240 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-18 05:19:35,173 supabase_client 18332 31240 Error authenticating user: Invalid API key
WARNING 2025-07-18 05:19:35,174 authentication 18332 31240 Supabase authentication <NAME_EMAIL>
ERROR 2025-07-18 05:19:56,239 views 18332 31240 Login error <NAME_EMAIL>: connection to server at "db.wmlsydbfwhevuaavjkje.supabase.co" (2406:da1a:6b0:f60c:7479:fc44:933a:100), port 5432 failed: Connection timed out (0x0000274C/10060)
	Is the server running on that host and accepting TCP/IP connections?

ERROR 2025-07-18 05:20:17,309 utils 18332 31240 Failed to create audit log: connection to server at "db.wmlsydbfwhevuaavjkje.supabase.co" (2406:da1a:6b0:f60c:7479:fc44:933a:100), port 5432 failed: Connection timed out (0x0000274C/10060)
	Is the server running on that host and accepting TCP/IP connections?

WARNING 2025-07-18 05:20:17,310 middleware 18332 31240 Slow request: POST /api/auth/login/ took 45.11s to process
ERROR 2025-07-18 05:20:38,384 utils 18332 31240 Failed to create audit log: connection to server at "db.wmlsydbfwhevuaavjkje.supabase.co" (2406:da1a:6b0:f60c:7479:fc44:933a:100), port 5432 failed: Connection timed out (0x0000274C/10060)
	Is the server running on that host and accepting TCP/IP connections?

WARNING 2025-07-18 05:20:38,386 middleware 18332 31240 Slow request: GET /api/accounts/ took 21.07s to process
ERROR 2025-07-18 05:20:59,734 utils 18332 31240 Failed to create audit log: connection to server at "db.wmlsydbfwhevuaavjkje.supabase.co" (2406:da1a:6b0:f60c:7479:fc44:933a:100), port 5432 failed: Connection timed out (0x0000274C/10060)
	Is the server running on that host and accepting TCP/IP connections?

WARNING 2025-07-18 05:20:59,736 middleware 18332 31240 Slow request: POST /api/auth/login/ took 21.09s to process
ERROR 2025-07-18 05:21:20,797 utils 18332 31240 Failed to create audit log: connection to server at "db.wmlsydbfwhevuaavjkje.supabase.co" (2406:da1a:6b0:f60c:7479:fc44:933a:100), port 5432 failed: Connection timed out (0x0000274C/10060)
	Is the server running on that host and accepting TCP/IP connections?

WARNING 2025-07-18 05:21:20,797 middleware 18332 31240 Slow request: POST /api/auth/login/ took 21.05s to process
WARNING 2025-07-18 05:21:42,920 middleware 18332 31240 Slow request: POST /api/auth/register/ took 22.11s to process
INFO 2025-07-18 11:27:22,722 supabase_client 3908 25108 Supabase client initialized successfully for exobank project
INFO 2025-07-18 11:28:02,833 supabase_client 26212 21704 Supabase client initialized successfully for exobank project
INFO 2025-07-18 11:28:26,308 supabase_client 27468 33568 Supabase client initialized successfully for exobank project
INFO 2025-07-18 11:33:57,666 supabase_client 21536 27032 Supabase client initialized successfully for exobank project
INFO 2025-07-18 12:06:31,761 supabase_client 12092 26828 Supabase client initialized successfully for exobank project
INFO 2025-07-18 15:48:00,300 autoreload 30076 14800 Watching for file changes with StatReloader
INFO 2025-07-18 15:48:02,175 supabase_client 30076 14800 Supabase client initialized successfully for exobank project
INFO 2025-07-18 19:15:09,189 supabase_client 9240 14260 Supabase client initialized successfully for exobank project
INFO 2025-07-18 19:34:29,251 supabase_client 4988 32964 Supabase client initialized successfully for exobank project
INFO 2025-07-18 19:34:47,049 supabase_client 23228 10048 Supabase client initialized successfully for exobank project
INFO 2025-07-18 22:23:18,636 autoreload 2820 22948 Watching for file changes with StatReloader
INFO 2025-07-18 22:23:20,542 supabase_client 2820 22948 Supabase client initialized successfully for exobank project
INFO 2025-07-18 22:28:32,864 autoreload 2820 22948 C:\Users\<USER>\Desktop\Django\exobank_new\backend\apps\users\views.py changed, reloading.
INFO 2025-07-18 22:28:36,923 autoreload 16596 29264 Watching for file changes with StatReloader
INFO 2025-07-18 22:28:39,749 supabase_client 16596 29264 Supabase client initialized successfully for exobank project
INFO 2025-07-18 22:28:51,036 supabase_client 24716 6352 Supabase client initialized successfully for exobank project
INFO 2025-07-18 22:29:12,733 autoreload 16596 29264 C:\Users\<USER>\Desktop\Django\exobank_new\backend\apps\admin_api\fix_endpoints.py changed, reloading.
INFO 2025-07-18 22:29:16,336 autoreload 15368 30380 Watching for file changes with StatReloader
INFO 2025-07-18 22:29:18,394 supabase_client 15368 30380 Supabase client initialized successfully for exobank project
INFO 2025-07-18 22:29:26,695 autoreload 15368 30380 C:\Users\<USER>\Desktop\Django\exobank_new\backend\apps\admin_api\fix_endpoints.py changed, reloading.
INFO 2025-07-18 22:29:30,488 autoreload 15236 30228 Watching for file changes with StatReloader
INFO 2025-07-18 22:29:33,108 supabase_client 15236 30228 Supabase client initialized successfully for exobank project
INFO 2025-07-18 22:29:38,093 supabase_client 31740 27996 Supabase client initialized successfully for exobank project
INFO 2025-07-18 22:29:50,438 autoreload 20120 29416 Watching for file changes with StatReloader
INFO 2025-07-18 22:29:54,128 supabase_client 20120 29416 Supabase client initialized successfully for exobank project
INFO 2025-07-18 22:48:21,844 autoreload 11908 8884 Watching for file changes with StatReloader
INFO 2025-07-18 22:48:25,020 supabase_client 11908 8884 Supabase client initialized successfully for exobank project
INFO 2025-07-19 01:38:14,787 supabase_client 16820 16824 Supabase client initialized successfully for exobank project
INFO 2025-07-19 01:38:44,045 autoreload 14040 4384 Watching for file changes with StatReloader
INFO 2025-07-19 01:38:54,215 supabase_client 14040 4384 Supabase client initialized successfully for exobank project
INFO 2025-07-19 11:20:29,822 autoreload 18764 9984 Watching for file changes with StatReloader
INFO 2025-07-19 11:20:35,023 supabase_client 18764 9984 Supabase client initialized successfully for exobank project
INFO 2025-07-19 11:21:31,730 autoreload 19120 18564 Watching for file changes with StatReloader
INFO 2025-07-19 11:21:35,405 supabase_client 19120 18564 Supabase client initialized successfully for exobank project
INFO 2025-07-19 12:46:29,337 supabase_client 8548 20628 Supabase client initialized successfully for exobank project
INFO 2025-07-19 12:46:48,330 supabase_client 22116 20780 Supabase client initialized successfully for exobank project
INFO 2025-07-19 12:47:49,274 supabase_client 2956 21788 Supabase client initialized successfully for exobank project
INFO 2025-07-19 12:49:17,888 autoreload 19120 18564 C:\Users\<USER>\Desktop\Django\exobank_new\backend\config\settings\base.py changed, reloading.
INFO 2025-07-19 12:49:29,180 autoreload 19596 2744 Watching for file changes with StatReloader
INFO 2025-07-19 12:49:34,260 supabase_client 19596 2744 Supabase client initialized successfully for exobank project
INFO 2025-07-19 12:49:36,862 supabase_client 12516 19664 Supabase client initialized successfully for exobank project
INFO 2025-07-19 12:54:32,977 supabase_client 20192 21984 Supabase client initialized successfully for exobank project
INFO 2025-07-19 12:54:53,282 supabase_client 13332 20320 Supabase client initialized successfully for exobank project
INFO 2025-07-19 12:55:08,436 supabase_client 17464 8976 Supabase client initialized successfully for exobank project
INFO 2025-07-19 12:58:17,706 autoreload 12912 17596 Watching for file changes with StatReloader
INFO 2025-07-19 12:58:20,751 supabase_client 12912 17596 Supabase client initialized successfully for exobank project
INFO 2025-07-19 20:52:36,365 autoreload 24260 15836 Watching for file changes with StatReloader
INFO 2025-07-19 20:52:39,182 supabase_client 24260 15836 Supabase client initialized successfully for exobank project
INFO 2025-07-19 21:44:50,938 basehttp 24260 15476 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-19 21:44:51,688 _client 24260 15476 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-19 21:44:51,705 supabase_client 24260 15476 Error authenticating user: Invalid API key
WARNING 2025-07-19 21:44:51,705 authentication 24260 15476 Supabase authentication <NAME_EMAIL>
WARNING 2025-07-19 21:44:52,987 views 24260 15476 Failed login attempt for email: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-19 21:44:53,022 middleware 24260 15476 Slow request: POST /api/auth/login/ took 1.98s to process
WARNING 2025-07-19 21:44:53,022 basehttp 24260 15476 "POST /api/auth/login/ HTTP/1.1" 401 172
INFO 2025-07-19 21:45:00,682 _client 24260 20468 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-19 21:45:00,688 supabase_client 24260 20468 Error authenticating user: Invalid API key
WARNING 2025-07-19 21:45:00,690 authentication 24260 20468 Supabase authentication <NAME_EMAIL>
WARNING 2025-07-19 21:45:02,459 views 24260 20468 Failed login attempt for email: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-19 21:45:02,467 middleware 24260 20468 Slow request: POST /api/auth/login/ took 1.89s to process
WARNING 2025-07-19 21:45:02,467 basehttp 24260 20468 "POST /api/auth/login/ HTTP/1.1" 401 172
INFO 2025-07-19 21:47:07,513 autoreload 24260 15836 C:\Users\<USER>\Desktop\Django\exobank_new\backend\apps\users\authentication.py changed, reloading.
INFO 2025-07-19 21:47:12,341 autoreload 14608 7200 Watching for file changes with StatReloader
INFO 2025-07-19 21:47:14,704 supabase_client 14608 7200 Supabase client initialized successfully for exobank project
INFO 2025-07-19 21:47:25,710 autoreload 14608 7200 C:\Users\<USER>\Desktop\Django\exobank_new\backend\apps\users\authentication.py changed, reloading.
INFO 2025-07-19 21:47:31,107 autoreload 3188 20624 Watching for file changes with StatReloader
INFO 2025-07-19 21:47:33,308 supabase_client 3188 20624 Supabase client initialized successfully for exobank project
INFO 2025-07-19 21:49:23,808 _client 3188 13264 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-19 21:49:23,821 supabase_client 3188 13264 Error authenticating user: Invalid API key
WARNING 2025-07-19 21:49:23,821 authentication 3188 13264 Supabase authentication <NAME_EMAIL>
INFO 2025-07-19 21:49:23,824 authentication 3188 13264 Trying Django fallback <NAME_EMAIL>
WARNING 2025-07-19 21:49:23,840 authentication 3188 13264 Django user not found: <EMAIL>
INFO 2025-07-19 21:49:23,840 authentication 3188 13264 Creating test user for development: <EMAIL>
INFO 2025-07-19 21:49:25,176 views 3188 13264 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-19 21:49:25,205 middleware 3188 13264 Slow request: POST /api/auth/login/ took 1.93s to process
INFO 2025-07-19 21:49:25,217 basehttp 3188 13264 "POST /api/auth/login/ HTTP/1.1" 200 1354
INFO 2025-07-19 21:50:58,420 autoreload 19892 22548 Watching for file changes with StatReloader
INFO 2025-07-19 21:51:02,294 supabase_client 19892 22548 Supabase client initialized successfully for exobank project
INFO 2025-07-19 21:51:33,058 basehttp 19892 10556 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-19 21:51:33,505 _client 19892 10556 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-19 21:51:33,505 supabase_client 19892 10556 Error authenticating user: Invalid API key
WARNING 2025-07-19 21:51:33,505 authentication 19892 10556 Supabase authentication <NAME_EMAIL>
INFO 2025-07-19 21:51:33,505 authentication 19892 10556 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-19 21:51:34,840 authentication 19892 10556 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-19 21:51:34,901 views 19892 10556 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-19 21:51:34,923 middleware 19892 10556 Slow request: POST /api/auth/login/ took 1.82s to process
INFO 2025-07-19 21:51:34,925 basehttp 19892 10556 "POST /api/auth/login/ HTTP/1.1" 200 1354
INFO 2025-07-19 21:51:39,952 _client 19892 10556 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-19 21:51:39,952 supabase_client 19892 10556 Error authenticating user: Invalid API key
WARNING 2025-07-19 21:51:39,966 authentication 19892 10556 Supabase authentication <NAME_EMAIL>
INFO 2025-07-19 21:51:39,967 authentication 19892 10556 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-19 21:51:41,350 authentication 19892 10556 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-19 21:51:41,409 views 19892 10556 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-19 21:51:41,432 middleware 19892 10556 Slow request: POST /api/auth/login/ took 1.55s to process
INFO 2025-07-19 21:51:41,434 basehttp 19892 10556 "POST /api/auth/login/ HTTP/1.1" 200 1354
INFO 2025-07-19 22:01:04,642 _client 19892 15556 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-19 22:01:04,642 supabase_client 19892 15556 Error authenticating user: Invalid API key
WARNING 2025-07-19 22:01:04,642 authentication 19892 15556 Supabase authentication <NAME_EMAIL>
INFO 2025-07-19 22:01:04,642 authentication 19892 15556 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-19 22:01:05,758 authentication 19892 15556 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-19 22:01:05,819 views 19892 15556 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-19 22:01:05,824 middleware 19892 15556 Slow request: POST /api/auth/login/ took 1.69s to process
INFO 2025-07-19 22:01:05,824 basehttp 19892 15556 "POST /api/auth/login/ HTTP/1.1" 200 1354
INFO 2025-07-19 22:25:18,656 supabase_client 16344 16312 Supabase client initialized successfully for exobank project
INFO 2025-07-19 22:25:35,324 supabase_client 4040 25076 Supabase client initialized successfully for exobank project
INFO 2025-07-19 22:50:35,211 supabase_client 14652 16860 Supabase client initialized successfully for exobank project
WARNING 2025-07-19 23:00:11,193 basehttp 14652 3428 "GET /api/auth/user/ HTTP/1.1" 404 9854
WARNING 2025-07-19 23:00:21,953 basehttp 14652 3428 "GET /api/auth/user/ HTTP/1.1" 404 9854
INFO 2025-07-19 23:06:43,720 basehttp 14652 9268 "GET /api/health/ HTTP/1.1" 200 92
INFO 2025-07-19 23:07:17,035 _client 14652 9268 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-19 23:07:17,035 supabase_client 14652 9268 Error authenticating user: Invalid API key
WARNING 2025-07-19 23:07:17,035 authentication 14652 9268 Supabase authentication <NAME_EMAIL>
INFO 2025-07-19 23:07:17,035 authentication 14652 9268 Trying Django fallback <NAME_EMAIL>
WARNING 2025-07-19 23:07:18,229 authentication 14652 9268 Invalid password for <NAME_EMAIL>
WARNING 2025-07-19 23:07:19,524 views 14652 9268 Failed login attempt for email: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-19 23:07:19,552 performance 14652 9268 Slow API request: POST /api/auth/login/ took 2.9900s
WARNING 2025-07-19 23:07:19,554 middleware 14652 9268 Slow request: POST /api/auth/login/ took 2.99s to process
WARNING 2025-07-19 23:07:19,554 basehttp 14652 9268 "POST /api/auth/login/ HTTP/1.1" 401 172
INFO 2025-07-19 23:08:07,030 _client 14652 9268 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-19 23:08:07,030 supabase_client 14652 9268 Error authenticating user: Invalid API key
WARNING 2025-07-19 23:08:07,030 authentication 14652 9268 Supabase authentication <NAME_EMAIL>
INFO 2025-07-19 23:08:07,030 authentication 14652 9268 Trying Django fallback <NAME_EMAIL>
WARNING 2025-07-19 23:08:08,387 authentication 14652 9268 Invalid password for <NAME_EMAIL>
WARNING 2025-07-19 23:08:09,635 views 14652 9268 Failed login attempt for email: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-19 23:08:09,635 performance 14652 9268 Slow API request: POST /api/auth/login/ took 2.7861s
WARNING 2025-07-19 23:08:09,647 middleware 14652 9268 Slow request: POST /api/auth/login/ took 2.80s to process
WARNING 2025-07-19 23:08:09,647 basehttp 14652 9268 "POST /api/auth/login/ HTTP/1.1" 401 172
INFO 2025-07-19 23:09:12,434 supabase_client 14152 4348 Supabase client initialized successfully for exobank project
INFO 2025-07-19 23:09:35,079 _client 14652 9268 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-19 23:09:35,081 supabase_client 14652 9268 Error authenticating user: Invalid API key
WARNING 2025-07-19 23:09:35,081 authentication 14652 9268 Supabase authentication <NAME_EMAIL>
INFO 2025-07-19 23:09:35,083 authentication 14652 9268 Trying Django fallback <NAME_EMAIL>
WARNING 2025-07-19 23:09:36,525 authentication 14652 9268 Invalid password for <NAME_EMAIL>
WARNING 2025-07-19 23:09:37,772 views 14652 9268 Failed login attempt for email: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-19 23:09:37,788 performance 14652 9268 Slow API request: POST /api/auth/login/ took 3.1078s
WARNING 2025-07-19 23:09:37,788 middleware 14652 9268 Slow request: POST /api/auth/login/ took 3.11s to process
WARNING 2025-07-19 23:09:37,788 basehttp 14652 9268 "POST /api/auth/login/ HTTP/1.1" 401 172
WARNING 2025-07-20 09:17:51,304 basehttp 14652 1748 "GET /api/auth/profile/ HTTP/1.1" 401 192
WARNING 2025-07-20 09:32:47,334 basehttp 14652 16876 "GET /api/auth/profile/ HTTP/1.1" 401 192
INFO 2025-07-20 09:32:47,948 _client 14652 16876 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 09:32:47,948 supabase_client 14652 16876 Error authenticating user: Invalid API key
WARNING 2025-07-20 09:32:47,948 authentication 14652 16876 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 09:32:47,948 authentication 14652 16876 Trying Django fallback <NAME_EMAIL>
WARNING 2025-07-20 09:32:47,966 authentication 14652 16876 Django user not found: <EMAIL>
WARNING 2025-07-20 09:32:49,387 views 14652 16876 Failed login attempt for email: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 09:32:49,413 performance 14652 16876 Slow API request: POST /api/auth/login/ took 2.0372s
WARNING 2025-07-20 09:32:49,422 middleware 14652 16876 Slow request: POST /api/auth/login/ took 2.04s to process
WARNING 2025-07-20 09:32:49,440 basehttp 14652 16876 "POST /api/auth/login/ HTTP/1.1" 401 172
INFO 2025-07-20 09:32:49,697 _client 14652 25564 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/signup "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 09:32:49,703 supabase_client 14652 25564 Error creating user in Supabase: Invalid API key
ERROR 2025-07-20 09:32:49,705 views 14652 25564 Registration <NAME_EMAIL>: Invalid API key
WARNING 2025-07-20 09:32:49,707 basehttp 14652 25564 "POST /api/auth/register/ HTTP/1.1" 400 83
WARNING 2025-07-20 09:32:49,739 views 14652 16876 Invalid refresh token: Token is invalid or expired
WARNING 2025-07-20 09:32:49,757 basehttp 14652 16876 "POST /api/auth/token/refresh/ HTTP/1.1" 401 95
WARNING 2025-07-20 09:32:49,789 basehttp 14652 25564 "GET /api/accounts/ HTTP/1.1" 401 192
WARNING 2025-07-20 09:32:49,811 basehttp 14652 16876 "GET /api/transactions/ HTTP/1.1" 401 192
WARNING 2025-07-20 09:32:49,832 basehttp 14652 25564 "GET /api/accounts/beneficiaries/ HTTP/1.1" 401 192
INFO 2025-07-20 09:34:16,115 _client 14652 3660 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 09:34:16,117 supabase_client 14652 3660 Error authenticating user: Invalid API key
WARNING 2025-07-20 09:34:16,119 authentication 14652 3660 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 09:34:16,119 authentication 14652 3660 Trying Django fallback <NAME_EMAIL>
WARNING 2025-07-20 09:34:17,498 authentication 14652 3660 Invalid password for <NAME_EMAIL>
WARNING 2025-07-20 09:34:19,344 views 14652 3660 Failed login attempt for email: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 09:34:19,355 performance 14652 3660 Slow API request: POST /api/auth/login/ took 3.3414s
WARNING 2025-07-20 09:34:19,359 middleware 14652 3660 Slow request: POST /api/auth/login/ took 3.34s to process
WARNING 2025-07-20 09:34:19,361 basehttp 14652 3660 "POST /api/auth/login/ HTTP/1.1" 401 172
INFO 2025-07-20 09:44:37,113 _client 14652 17304 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 09:44:37,124 supabase_client 14652 17304 Error authenticating user: Invalid API key
WARNING 2025-07-20 09:44:37,138 authentication 14652 17304 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 09:44:37,140 authentication 14652 17304 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 09:44:38,560 authentication 14652 17304 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 09:44:38,685 views 14652 17304 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 09:44:38,799 performance 14652 17304 Slow API request: POST /api/auth/login/ took 2.1054s
WARNING 2025-07-20 09:44:38,799 middleware 14652 17304 Slow request: POST /api/auth/login/ took 2.11s to process
INFO 2025-07-20 09:44:38,802 basehttp 14652 17304 "POST /api/auth/login/ HTTP/1.1" 200 1348
INFO 2025-07-20 09:44:38,891 basehttp 14652 17304 "GET /api/accounts/ HTTP/1.1" 200 176
INFO 2025-07-20 09:44:38,936 basehttp 14652 17304 "GET /api/accounts/ HTTP/1.1" 200 176
INFO 2025-07-20 09:44:38,975 basehttp 14652 17304 "GET /api/accounts/ HTTP/1.1" 200 176
WARNING 2025-07-20 09:44:39,035 basehttp 14652 17304 "GET /api/accounts/invalid-id/ HTTP/1.1" 404 6039
INFO 2025-07-20 09:46:09,015 _client 14652 13292 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 09:46:09,015 supabase_client 14652 13292 Error authenticating user: Invalid API key
WARNING 2025-07-20 09:46:09,015 authentication 14652 13292 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 09:46:09,015 authentication 14652 13292 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 09:46:10,495 authentication 14652 13292 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 09:46:10,547 views 14652 13292 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 09:46:10,568 middleware 14652 13292 Slow request: POST /api/auth/login/ took 1.63s to process
INFO 2025-07-20 09:46:10,570 basehttp 14652 13292 "POST /api/auth/login/ HTTP/1.1" 200 1348
INFO 2025-07-20 09:46:10,628 basehttp 14652 13292 "GET /api/accounts/ HTTP/1.1" 200 936
INFO 2025-07-20 09:46:10,673 basehttp 14652 13292 "GET /api/accounts/ HTTP/1.1" 200 936
INFO 2025-07-20 09:46:10,715 basehttp 14652 13292 "GET /api/accounts/ HTTP/1.1" 200 936
WARNING 2025-07-20 09:46:10,769 basehttp 14652 13292 "GET /api/accounts/invalid-id/ HTTP/1.1" 404 6039
INFO 2025-07-20 09:47:57,994 _client 14652 23228 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 09:47:57,996 supabase_client 14652 23228 Error authenticating user: Invalid API key
WARNING 2025-07-20 09:47:57,998 authentication 14652 23228 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 09:47:57,998 authentication 14652 23228 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 09:47:59,682 authentication 14652 23228 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 09:47:59,742 views 14652 23228 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 09:47:59,761 performance 14652 23228 Slow API request: POST /api/auth/login/ took 2.1700s
WARNING 2025-07-20 09:47:59,763 middleware 14652 23228 Slow request: POST /api/auth/login/ took 2.17s to process
INFO 2025-07-20 09:47:59,765 basehttp 14652 23228 "POST /api/auth/login/ HTTP/1.1" 200 1348
INFO 2025-07-20 09:47:59,828 basehttp 14652 23228 "GET /api/accounts/ HTTP/1.1" 200 936
INFO 2025-07-20 09:49:07,725 _client 14652 21456 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 09:49:07,727 supabase_client 14652 21456 Error authenticating user: Invalid API key
WARNING 2025-07-20 09:49:07,729 authentication 14652 21456 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 09:49:07,730 authentication 14652 21456 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 09:49:08,984 authentication 14652 21456 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 09:49:09,042 views 14652 21456 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 09:49:09,058 middleware 14652 21456 Slow request: POST /api/auth/login/ took 1.40s to process
INFO 2025-07-20 09:49:09,060 basehttp 14652 21456 "POST /api/auth/login/ HTTP/1.1" 200 1348
INFO 2025-07-20 09:49:09,108 basehttp 14652 21456 "GET /api/accounts/ HTTP/1.1" 200 936
INFO 2025-07-20 09:49:09,147 basehttp 14652 21456 "GET /api/accounts/ HTTP/1.1" 200 936
INFO 2025-07-20 09:49:09,180 basehttp 14652 21456 "GET /api/accounts/********-2a83-4ea1-b0ca-50f98a64464d/ HTTP/1.1" 200 448
INFO 2025-07-20 09:49:09,219 basehttp 14652 21456 "GET /api/accounts/ HTTP/1.1" 200 936
WARNING 2025-07-20 09:49:09,266 basehttp 14652 21456 "GET /api/accounts/invalid-id/ HTTP/1.1" 404 6039
INFO 2025-07-20 09:51:28,989 _client 14652 11344 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 09:51:28,989 supabase_client 14652 11344 Error authenticating user: Invalid API key
WARNING 2025-07-20 09:51:28,989 authentication 14652 11344 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 09:51:28,989 authentication 14652 11344 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 09:51:30,721 authentication 14652 11344 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 09:51:30,768 views 14652 11344 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 09:51:30,786 performance 14652 11344 Slow API request: POST /api/auth/login/ took 2.1886s
WARNING 2025-07-20 09:51:30,786 middleware 14652 11344 Slow request: POST /api/auth/login/ took 2.19s to process
INFO 2025-07-20 09:51:30,786 basehttp 14652 11344 "POST /api/auth/login/ HTTP/1.1" 200 1348
INFO 2025-07-20 09:51:30,895 basehttp 14652 11344 "GET /api/transactions/ HTTP/1.1" 200 2118
INFO 2025-07-20 09:51:30,970 basehttp 14652 11344 "GET /api/transactions/ HTTP/1.1" 200 2118
INFO 2025-07-20 09:51:31,008 basehttp 14652 11344 "GET /api/transactions/ae129de8-e9e6-4a3f-8e14-0009a5a5dbcf/ HTTP/1.1" 200 782
INFO 2025-07-20 09:51:31,081 basehttp 14652 11344 "GET /api/accounts/ HTTP/1.1" 200 936
INFO 2025-07-20 09:51:31,204 basehttp 14652 11344 "GET /api/transactions/?from_account=********-2a83-4ea1-b0ca-50f98a64464d HTTP/1.1" 200 176
INFO 2025-07-20 09:51:31,259 basehttp 14652 11344 "GET /api/transactions/ HTTP/1.1" 200 2118
INFO 2025-07-20 09:51:31,307 basehttp 14652 11344 "GET /api/transactions/?page_size=2 HTTP/1.1" 200 1505
INFO 2025-07-20 09:54:17,729 _client 14652 1712 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 09:54:17,755 supabase_client 14652 1712 Error authenticating user: Invalid API key
WARNING 2025-07-20 09:54:17,762 authentication 14652 1712 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 09:54:17,795 authentication 14652 1712 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 09:54:23,974 authentication 14652 1712 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 09:54:24,080 views 14652 1712 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 09:54:24,141 performance 14652 1712 Slow API request: POST /api/auth/login/ took 7.0881s
WARNING 2025-07-20 09:54:24,143 middleware 14652 1712 Slow request: POST /api/auth/login/ took 7.09s to process
INFO 2025-07-20 09:54:24,147 basehttp 14652 1712 "POST /api/auth/login/ HTTP/1.1" 200 1348
INFO 2025-07-20 09:54:24,391 basehttp 14652 1712 "GET /api/accounts/beneficiaries/ HTTP/1.1" 200 800
INFO 2025-07-20 09:54:24,561 basehttp 14652 1712 "GET /api/accounts/beneficiaries/ HTTP/1.1" 200 800
INFO 2025-07-20 09:54:24,658 basehttp 14652 1712 "GET /api/accounts/beneficiaries/05d9ec6f-568b-43fb-8ed7-fbd2cf773328/ HTTP/1.1" 200 380
INFO 2025-07-20 09:54:24,903 basehttp 14652 1712 "POST /api/accounts/beneficiaries/ HTTP/1.1" 201 372
INFO 2025-07-20 09:54:25,040 basehttp 14652 1712 "PATCH /api/accounts/beneficiaries/a066da21-5d56-4b45-be35-c718ff5c64c1/ HTTP/1.1" 200 388
INFO 2025-07-20 09:54:25,287 basehttp 14652 1712 "GET /api/accounts/beneficiaries/?is_active=true HTTP/1.1" 200 1123
INFO 2025-07-20 09:54:25,436 basehttp 14652 1712 "DELETE /api/accounts/beneficiaries/a066da21-5d56-4b45-be35-c718ff5c64c1/ HTTP/1.1" 204 58
INFO 2025-07-20 09:54:25,565 basehttp 14652 1712 - Broken pipe from ('127.0.0.1', 38256)
WARNING 2025-07-20 10:07:10,548 basehttp 14652 2748 "GET /api/admin/health/ HTTP/1.1" 401 192
WARNING 2025-07-20 10:07:19,545 basehttp 14652 2748 "GET /api/admin/health/ HTTP/1.1" 401 192
WARNING 2025-07-20 13:05:56,900 basehttp 14652 12228 "GET /api/admin/health/ HTTP/1.1" 401 192
INFO 2025-07-20 13:05:59,464 basehttp 14652 12228 - Broken pipe from ('127.0.0.1', 40444)
INFO 2025-07-20 15:57:23,810 supabase_client 19892 12844 Supabase client initialized successfully for exobank project
WARNING 2025-07-20 16:52:02,088 basehttp 19892 27768 "GET /api/admin/health/ HTTP/1.1" 401 192
WARNING 2025-07-20 16:52:02,147 basehttp 19892 27768 "POST /api/auth/login/ HTTP/1.1" 400 230
INFO 2025-07-20 16:52:02,150 basehttp 19892 27768 code 400, message Bad request syntax ('33')
WARNING 2025-07-20 16:52:02,151 basehttp 19892 27768 "33" 400 -
WARNING 2025-07-20 16:52:02,182 basehttp 19892 29572 "POST /api/auth/login/ HTTP/1.1" 400 230
INFO 2025-07-20 16:52:02,184 basehttp 19892 29572 code 400, message Bad request syntax ('33')
WARNING 2025-07-20 16:52:02,185 basehttp 19892 29572 "33" 400 -
WARNING 2025-07-20 16:52:02,230 basehttp 19892 25468 "GET /api/admin/dashboard/stats/ HTTP/1.1" 401 192
INFO 2025-07-20 16:52:02,263 basehttp 19892 25468 - Broken pipe from ('127.0.0.1', 65438)
WARNING 2025-07-20 16:52:47,582 basehttp 19892 2308 "GET /api/admin/health/ HTTP/1.1" 401 192
INFO 2025-07-20 16:52:48,288 _client 19892 2308 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 16:52:48,300 supabase_client 19892 2308 Error authenticating user: Invalid API key
WARNING 2025-07-20 16:52:48,301 authentication 19892 2308 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 16:52:48,303 authentication 19892 2308 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 16:52:49,756 authentication 19892 2308 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 16:52:50,008 views 19892 2308 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 16:52:50,080 performance 19892 2308 Slow API request: POST /api/auth/login/ took 2.4819s
WARNING 2025-07-20 16:52:50,081 middleware 19892 2308 Slow request: POST /api/auth/login/ took 2.48s to process
INFO 2025-07-20 16:52:50,083 basehttp 19892 2308 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-20 16:52:50,114 _client 19892 2308 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 16:52:50,115 supabase_client 19892 2308 Error authenticating user: Invalid API key
WARNING 2025-07-20 16:52:50,117 authentication 19892 2308 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 16:52:50,117 authentication 19892 2308 Trying Django fallback <NAME_EMAIL>
WARNING 2025-07-20 16:52:50,125 authentication 19892 2308 Django user not found: <EMAIL>
WARNING 2025-07-20 16:52:51,440 views 19892 2308 Failed login attempt for email: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 16:52:51,450 middleware 19892 2308 Slow request: POST /api/auth/login/ took 1.36s to process
WARNING 2025-07-20 16:52:51,452 basehttp 19892 2308 "POST /api/auth/login/ HTTP/1.1" 401 172
INFO 2025-07-20 16:52:51,482 _client 19892 2308 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 16:52:51,485 supabase_client 19892 2308 Error authenticating user: Invalid API key
WARNING 2025-07-20 16:52:51,485 authentication 19892 2308 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 16:52:51,486 authentication 19892 2308 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 16:52:53,033 authentication 19892 2308 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 16:52:53,416 views 19892 2308 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 16:52:53,437 middleware 19892 2308 Slow request: POST /api/auth/login/ took 1.98s to process
INFO 2025-07-20 16:52:53,439 basehttp 19892 2308 "POST /api/auth/login/ HTTP/1.1" 200 1340
INFO 2025-07-20 16:52:53,470 _client 19892 2308 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 16:52:53,473 supabase_client 19892 2308 Error authenticating user: Invalid API key
WARNING 2025-07-20 16:52:53,474 authentication 19892 2308 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 16:52:53,476 authentication 19892 2308 Trying Django fallback <NAME_EMAIL>
WARNING 2025-07-20 16:52:53,485 authentication 19892 2308 Django user not found: <EMAIL>
WARNING 2025-07-20 16:52:54,628 views 19892 2308 Failed login attempt for email: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 16:52:54,642 middleware 19892 2308 Slow request: POST /api/auth/login/ took 1.20s to process
WARNING 2025-07-20 16:52:54,644 basehttp 19892 2308 "POST /api/auth/login/ HTTP/1.1" 401 172
WARNING 2025-07-20 16:52:54,693 performance 19892 2308 High query count: get_cached_stats executed 14 queries in 0.0359s
INFO 2025-07-20 16:52:54,751 basehttp 19892 2308 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 16:52:54,846 basehttp 19892 2308 "GET /api/admin/users/ HTTP/1.1" 200 1749
INFO 2025-07-20 16:52:54,919 basehttp 19892 2308 "GET /api/admin/audit/logs/ HTTP/1.1" 200 13644
WARNING 2025-07-20 16:52:54,942 basehttp 19892 2308 "GET /api/admin/dashboard/stats/ HTTP/1.1" 401 192
INFO 2025-07-20 16:52:54,967 basehttp 19892 2308 - Broken pipe from ('127.0.0.1', 65490)
INFO 2025-07-20 16:53:16,200 _client 19892 23792 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 16:53:16,202 supabase_client 19892 23792 Error authenticating user: Invalid API key
WARNING 2025-07-20 16:53:16,202 authentication 19892 23792 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 16:53:16,203 authentication 19892 23792 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 16:53:17,640 authentication 19892 23792 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 16:53:17,708 views 19892 23792 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 16:53:17,725 middleware 19892 23792 Slow request: POST /api/auth/login/ took 1.60s to process
INFO 2025-07-20 16:53:17,727 basehttp 19892 23792 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-20 16:55:02,318 _client 19892 15556 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 16:55:02,322 supabase_client 19892 15556 Error authenticating user: Invalid API key
WARNING 2025-07-20 16:55:02,323 authentication 19892 15556 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 16:55:02,325 authentication 19892 15556 Trying Django fallback <NAME_EMAIL>
WARNING 2025-07-20 16:55:02,338 authentication 19892 15556 Django user not found: <EMAIL>
WARNING 2025-07-20 16:55:03,524 views 19892 15556 Failed login attempt for email: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 16:55:03,536 middleware 19892 15556 Slow request: POST /api/auth/login/ took 1.66s to process
WARNING 2025-07-20 16:55:03,537 basehttp 19892 15556 "POST /api/auth/login/ HTTP/1.1" 401 172
WARNING 2025-07-20 16:58:28,545 basehttp 19892 1556 "GET /api/admin/health/ HTTP/1.1" 401 192
INFO 2025-07-20 16:58:29,011 _client 19892 1556 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 16:58:29,013 supabase_client 19892 1556 Error authenticating user: Invalid API key
WARNING 2025-07-20 16:58:29,014 authentication 19892 1556 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 16:58:29,014 authentication 19892 1556 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 16:58:31,387 authentication 19892 1556 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 16:58:31,526 views 19892 1556 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 16:58:31,552 performance 19892 1556 Slow API request: POST /api/auth/login/ took 2.9765s
WARNING 2025-07-20 16:58:31,553 middleware 19892 1556 Slow request: POST /api/auth/login/ took 2.98s to process
INFO 2025-07-20 16:58:31,555 basehttp 19892 1556 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-20 16:58:31,592 _client 19892 1556 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 16:58:31,594 supabase_client 19892 1556 Error authenticating user: Invalid API key
WARNING 2025-07-20 16:58:31,594 authentication 19892 1556 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 16:58:31,595 authentication 19892 1556 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 16:58:34,272 authentication 19892 1556 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 16:58:34,383 views 19892 1556 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 16:58:34,416 performance 19892 1556 Slow API request: POST /api/auth/login/ took 2.8534s
WARNING 2025-07-20 16:58:34,418 middleware 19892 1556 Slow request: POST /api/auth/login/ took 2.85s to process
INFO 2025-07-20 16:58:34,421 basehttp 19892 1556 "POST /api/auth/login/ HTTP/1.1" 200 1340
INFO 2025-07-20 16:58:34,498 _client 19892 1556 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 16:58:34,500 supabase_client 19892 1556 Error authenticating user: Invalid API key
WARNING 2025-07-20 16:58:34,500 authentication 19892 1556 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 16:58:34,502 authentication 19892 1556 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 16:58:37,180 authentication 19892 1556 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 16:58:37,233 views 19892 1556 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 16:58:37,253 performance 19892 1556 Slow API request: POST /api/auth/login/ took 2.8045s
WARNING 2025-07-20 16:58:37,254 middleware 19892 1556 Slow request: POST /api/auth/login/ took 2.81s to process
INFO 2025-07-20 16:58:37,256 basehttp 19892 1556 "POST /api/auth/login/ HTTP/1.1" 200 1340
INFO 2025-07-20 16:58:37,308 _client 19892 1556 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 16:58:37,310 supabase_client 19892 1556 Error authenticating user: Invalid API key
WARNING 2025-07-20 16:58:37,311 authentication 19892 1556 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 16:58:37,311 authentication 19892 1556 Trying Django fallback <NAME_EMAIL>
WARNING 2025-07-20 16:58:37,320 authentication 19892 1556 Django user not found: <EMAIL>
WARNING 2025-07-20 16:58:38,967 views 19892 1556 Failed login attempt for email: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 16:58:38,980 middleware 19892 1556 Slow request: POST /api/auth/login/ took 1.72s to process
WARNING 2025-07-20 16:58:38,982 basehttp 19892 1556 "POST /api/auth/login/ HTTP/1.1" 401 172
WARNING 2025-07-20 16:58:39,025 performance 19892 1556 High query count: get_cached_stats executed 14 queries in 0.0299s
INFO 2025-07-20 16:58:39,070 basehttp 19892 1556 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 16:58:39,148 basehttp 19892 1556 "GET /api/admin/users/ HTTP/1.1" 200 1749
INFO 2025-07-20 16:58:39,216 basehttp 19892 1556 "GET /api/admin/audit/logs/ HTTP/1.1" 200 20576
WARNING 2025-07-20 16:58:39,254 basehttp 19892 1556 "GET /api/admin/dashboard/stats/ HTTP/1.1" 401 192
INFO 2025-07-20 16:58:39,283 basehttp 19892 1556 - Broken pipe from ('127.0.0.1', 1365)
WARNING 2025-07-20 16:59:14,738 basehttp 19892 18844 "GET /api/admin/health/ HTTP/1.1" 401 192
INFO 2025-07-20 16:59:14,825 _client 19892 18844 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 16:59:14,829 supabase_client 19892 18844 Error authenticating user: Invalid API key
WARNING 2025-07-20 16:59:14,830 authentication 19892 18844 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 16:59:14,831 authentication 19892 18844 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 16:59:17,750 authentication 19892 18844 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 16:59:17,812 views 19892 18844 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 16:59:17,831 performance 19892 18844 Slow API request: POST /api/auth/login/ took 3.0726s
WARNING 2025-07-20 16:59:17,832 middleware 19892 18844 Slow request: POST /api/auth/login/ took 3.07s to process
INFO 2025-07-20 16:59:17,834 basehttp 19892 18844 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-20 16:59:17,865 _client 19892 18844 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 16:59:17,866 supabase_client 19892 18844 Error authenticating user: Invalid API key
WARNING 2025-07-20 16:59:17,867 authentication 19892 18844 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 16:59:17,867 authentication 19892 18844 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 16:59:20,853 authentication 19892 18844 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 16:59:20,982 views 19892 18844 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 16:59:21,000 performance 19892 18844 Slow API request: POST /api/auth/login/ took 3.1583s
WARNING 2025-07-20 16:59:21,000 middleware 19892 18844 Slow request: POST /api/auth/login/ took 3.16s to process
INFO 2025-07-20 16:59:21,004 basehttp 19892 18844 "POST /api/auth/login/ HTTP/1.1" 200 1340
INFO 2025-07-20 16:59:21,044 _client 19892 18844 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 16:59:21,045 supabase_client 19892 18844 Error authenticating user: Invalid API key
WARNING 2025-07-20 16:59:21,046 authentication 19892 18844 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 16:59:21,047 authentication 19892 18844 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 16:59:23,647 authentication 19892 18844 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 16:59:23,740 views 19892 18844 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 16:59:23,763 performance 19892 18844 Slow API request: POST /api/auth/login/ took 2.7464s
WARNING 2025-07-20 16:59:23,763 middleware 19892 18844 Slow request: POST /api/auth/login/ took 2.75s to process
INFO 2025-07-20 16:59:23,765 basehttp 19892 18844 "POST /api/auth/login/ HTTP/1.1" 200 1340
INFO 2025-07-20 16:59:23,803 _client 19892 18844 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 16:59:23,805 supabase_client 19892 18844 Error authenticating user: Invalid API key
WARNING 2025-07-20 16:59:23,806 authentication 19892 18844 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 16:59:23,807 authentication 19892 18844 Trying Django fallback <NAME_EMAIL>
WARNING 2025-07-20 16:59:23,814 authentication 19892 18844 Django user not found: <EMAIL>
WARNING 2025-07-20 16:59:26,624 views 19892 18844 Failed login attempt for email: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 16:59:26,639 performance 19892 18844 Slow API request: POST /api/auth/login/ took 2.8661s
WARNING 2025-07-20 16:59:26,641 middleware 19892 18844 Slow request: POST /api/auth/login/ took 2.87s to process
WARNING 2025-07-20 16:59:26,644 basehttp 19892 18844 "POST /api/auth/login/ HTTP/1.1" 401 172
INFO 2025-07-20 16:59:26,708 basehttp 19892 18844 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 16:59:26,800 basehttp 19892 18844 "GET /api/admin/users/ HTTP/1.1" 200 1749
INFO 2025-07-20 16:59:26,874 basehttp 19892 18844 "GET /api/admin/audit/logs/ HTTP/1.1" 200 20241
WARNING 2025-07-20 16:59:26,901 basehttp 19892 18844 "GET /api/admin/dashboard/stats/ HTTP/1.1" 401 192
INFO 2025-07-20 16:59:26,922 basehttp 19892 18844 - Broken pipe from ('127.0.0.1', 1417)
INFO 2025-07-20 17:01:47,568 _client 19892 25884 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 17:01:47,570 supabase_client 19892 25884 Error authenticating user: Invalid API key
WARNING 2025-07-20 17:01:47,572 authentication 19892 25884 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 17:01:47,574 authentication 19892 25884 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 17:01:49,087 authentication 19892 25884 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 17:01:49,153 views 19892 25884 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 17:01:49,180 performance 19892 25884 Slow API request: POST /api/auth/login/ took 2.0341s
WARNING 2025-07-20 17:01:49,182 middleware 19892 25884 Slow request: POST /api/auth/login/ took 2.04s to process
INFO 2025-07-20 17:01:49,185 basehttp 19892 25884 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-20 17:01:49,273 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:49,329 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:49,398 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:49,557 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:49,721 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:49,887 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:49,938 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:49,998 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:50,055 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:50,111 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:50,171 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:50,201 _client 19892 25884 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 17:01:50,203 supabase_client 19892 25884 Error authenticating user: Invalid API key
WARNING 2025-07-20 17:01:50,204 authentication 19892 25884 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 17:01:50,205 authentication 19892 25884 Trying Django fallback <NAME_EMAIL>
WARNING 2025-07-20 17:01:50,213 authentication 19892 25884 Django user not found: <EMAIL>
WARNING 2025-07-20 17:01:51,441 views 19892 25884 Failed login attempt for email: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 17:01:51,450 middleware 19892 25884 Slow request: POST /api/auth/login/ took 1.28s to process
WARNING 2025-07-20 17:01:51,452 basehttp 19892 25884 "POST /api/auth/login/ HTTP/1.1" 401 172
INFO 2025-07-20 17:01:52,525 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:52,581 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:52,631 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:52,673 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:52,714 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:52,756 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:52,798 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:52,850 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:52,892 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:52,937 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:52,979 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:01:53,024 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
WARNING 2025-07-20 17:01:53,084 basehttp 19892 25884 "GET /api/admin/dashboard/stats/ HTTP/1.1" 401 310
INFO 2025-07-20 17:01:53,104 basehttp 19892 25884 - Broken pipe from ('127.0.0.1', 1640)
INFO 2025-07-20 17:02:15,538 _client 19892 27752 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 17:02:15,540 supabase_client 19892 27752 Error authenticating user: Invalid API key
WARNING 2025-07-20 17:02:15,540 authentication 19892 27752 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 17:02:15,541 authentication 19892 27752 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 17:02:16,668 authentication 19892 27752 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 17:02:16,720 views 19892 27752 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 17:02:16,736 middleware 19892 27752 Slow request: POST /api/auth/login/ took 1.27s to process
INFO 2025-07-20 17:02:16,738 basehttp 19892 27752 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-20 17:02:16,792 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:02:16,858 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:02:16,902 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:02:17,063 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:02:17,214 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:02:17,363 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:02:17,437 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:02:17,496 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:02:17,551 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:02:17,610 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:02:17,657 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:02:17,679 _client 19892 27752 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 17:02:17,681 supabase_client 19892 27752 Error authenticating user: Invalid API key
WARNING 2025-07-20 17:02:17,681 authentication 19892 27752 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 17:02:17,682 authentication 19892 27752 Trying Django fallback <NAME_EMAIL>
WARNING 2025-07-20 17:02:17,688 authentication 19892 27752 Django user not found: <EMAIL>
WARNING 2025-07-20 17:02:18,767 views 19892 27752 Failed login attempt for email: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 17:02:18,778 middleware 19892 27752 Slow request: POST /api/auth/login/ took 1.12s to process
WARNING 2025-07-20 17:02:18,781 basehttp 19892 27752 "POST /api/auth/login/ HTTP/1.1" 401 172
INFO 2025-07-20 17:02:19,862 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:02:19,936 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:02:20,072 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:02:20,160 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:02:20,270 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:02:20,318 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:02:20,369 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:02:20,488 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
WARNING 2025-07-20 17:02:21,622 middleware 19892 27752 Slow request: GET /api/admin/dashboard/stats/ took 1.10s to process
INFO 2025-07-20 17:02:21,622 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:02:21,718 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:02:21,781 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
INFO 2025-07-20 17:02:21,871 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 200 279
WARNING 2025-07-20 17:02:21,900 basehttp 19892 27752 "GET /api/admin/dashboard/stats/ HTTP/1.1" 401 310
INFO 2025-07-20 17:02:21,918 basehttp 19892 27752 - Broken pipe from ('127.0.0.1', 1690)
INFO 2025-07-20 17:05:12,738 _client 19892 30308 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 17:05:12,746 supabase_client 19892 30308 Error authenticating user: Invalid API key
WARNING 2025-07-20 17:05:12,748 authentication 19892 30308 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 17:05:12,749 authentication 19892 30308 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 17:05:13,835 authentication 19892 30308 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 17:05:13,898 views 19892 30308 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 17:05:13,921 middleware 19892 30308 Slow request: POST /api/auth/login/ took 1.63s to process
INFO 2025-07-20 17:05:13,923 basehttp 19892 30308 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-20 17:05:14,035 basehttp 19892 30308 "GET /api/admin/users/ HTTP/1.1" 200 1749
WARNING 2025-07-20 17:05:14,108 basehttp 19892 30308 "POST /api/admin/users/ HTTP/1.1" 405 159
INFO 2025-07-20 17:05:14,186 basehttp 19892 30308 "GET /api/admin/users/?search=admin HTTP/1.1" 200 567
INFO 2025-07-20 17:05:14,240 basehttp 19892 30308 "GET /api/admin/users/?role=admin HTTP/1.1" 200 176
INFO 2025-07-20 17:05:14,312 basehttp 19892 30308 "GET /api/admin/users/?page_size=2 HTTP/1.1" 200 951
WARNING 2025-07-20 17:05:14,380 basehttp 19892 30308 "POST /api/admin/users/ HTTP/1.1" 405 159
INFO 2025-07-20 17:05:14,403 basehttp 19892 30308 - Broken pipe from ('127.0.0.1', 1901)
INFO 2025-07-20 17:19:33,856 _client 19892 31116 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 17:19:33,861 supabase_client 19892 31116 Error authenticating user: Invalid API key
WARNING 2025-07-20 17:19:33,863 authentication 19892 31116 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 17:19:33,863 authentication 19892 31116 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 17:19:35,259 authentication 19892 31116 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 17:19:35,320 views 19892 31116 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 17:19:35,352 middleware 19892 31116 Slow request: POST /api/auth/login/ took 1.93s to process
INFO 2025-07-20 17:19:35,354 basehttp 19892 31116 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-20 17:19:35,464 basehttp 19892 31116 "GET /api/admin/users/ HTTP/1.1" 200 1749
INFO 2025-07-20 17:26:57,124 _client 19892 10104 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 17:26:57,132 supabase_client 19892 10104 Error authenticating user: Invalid API key
WARNING 2025-07-20 17:26:57,134 authentication 19892 10104 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 17:26:57,135 authentication 19892 10104 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 17:26:58,479 authentication 19892 10104 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 17:26:58,555 views 19892 10104 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 17:26:58,584 middleware 19892 10104 Slow request: POST /api/auth/login/ took 1.92s to process
INFO 2025-07-20 17:26:58,588 basehttp 19892 10104 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-20 17:26:58,705 basehttp 19892 10104 "GET /api/admin/users/ HTTP/1.1" 200 1749
INFO 2025-07-20 17:37:25,932 _client 19892 9960 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 17:37:25,940 supabase_client 19892 9960 Error authenticating user: Invalid API key
WARNING 2025-07-20 17:37:25,942 authentication 19892 9960 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 17:37:25,942 authentication 19892 9960 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 17:37:27,267 authentication 19892 9960 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 17:37:27,327 views 19892 9960 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 17:37:27,360 middleware 19892 9960 Slow request: POST /api/auth/login/ took 1.92s to process
INFO 2025-07-20 17:37:27,364 basehttp 19892 9960 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-20 17:37:27,482 basehttp 19892 9960 "GET /api/admin/users/ HTTP/1.1" 200 1749
INFO 2025-07-20 17:37:27,592 basehttp 19892 9960 "GET /api/admin/users/8970a26f-bf93-43de-b044-508f76d5ea2a/ HTTP/1.1" 200 493
INFO 2025-07-20 17:37:27,680 basehttp 19892 9960 "GET /api/admin/users/?search=admin HTTP/1.1" 200 567
INFO 2025-07-20 17:37:27,738 basehttp 19892 9960 "GET /api/admin/users/?role=staff HTTP/1.1" 200 561
INFO 2025-07-20 17:37:27,759 basehttp 19892 9960 - Broken pipe from ('127.0.0.1', 3467)
INFO 2025-07-20 17:38:21,223 _client 19892 3612 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 17:38:21,225 supabase_client 19892 3612 Error authenticating user: Invalid API key
WARNING 2025-07-20 17:38:21,225 authentication 19892 3612 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 17:38:21,226 authentication 19892 3612 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 17:38:22,245 authentication 19892 3612 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 17:38:22,291 views 19892 3612 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 17:38:22,306 middleware 19892 3612 Slow request: POST /api/auth/login/ took 1.16s to process
INFO 2025-07-20 17:38:22,307 basehttp 19892 3612 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-20 17:38:22,397 basehttp 19892 3612 "GET /api/admin/users/ HTTP/1.1" 200 1749
INFO 2025-07-20 17:38:22,516 basehttp 19892 3612 "GET /api/admin/users/8970a26f-bf93-43de-b044-508f76d5ea2a HTTP/1.1" 301 0
WARNING 2025-07-20 17:38:22,553 basehttp 19892 28488 "GET /api/admin/users/8970a26f-bf93-43de-b044-508f76d5ea2a/ HTTP/1.1" 401 192
INFO 2025-07-20 17:40:02,160 _client 19892 16376 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 17:40:02,163 supabase_client 19892 16376 Error authenticating user: Invalid API key
WARNING 2025-07-20 17:40:02,164 authentication 19892 16376 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 17:40:02,164 authentication 19892 16376 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 17:40:03,614 authentication 19892 16376 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 17:40:03,670 views 19892 16376 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 17:40:03,686 middleware 19892 16376 Slow request: POST /api/auth/login/ took 1.93s to process
INFO 2025-07-20 17:40:03,688 basehttp 19892 16376 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-20 17:40:03,768 basehttp 19892 16376 "GET /api/admin/users/ HTTP/1.1" 200 1749
INFO 2025-07-20 17:40:03,797 basehttp 19892 16376 "GET /api/admin/users/8970a26f-bf93-43de-b044-508f76d5ea2a HTTP/1.1" 301 0
INFO 2025-07-20 17:40:03,862 basehttp 19892 17072 "GET /api/admin/users/?search=admin HTTP/1.1" 200 567
INFO 2025-07-20 17:40:03,923 basehttp 19892 17072 "GET /api/admin/users/?role=staff HTTP/1.1" 200 561
INFO 2025-07-20 17:40:03,949 basehttp 19892 17072 - Broken pipe from ('127.0.0.1', 3650)
INFO 2025-07-20 17:47:10,873 _client 19892 18304 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 17:47:10,894 supabase_client 19892 18304 Error authenticating user: Invalid API key
WARNING 2025-07-20 17:47:10,896 authentication 19892 18304 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 17:47:10,897 authentication 19892 18304 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 17:47:12,052 authentication 19892 18304 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 17:47:12,113 views 19892 18304 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 17:47:12,140 middleware 19892 18304 Slow request: POST /api/auth/login/ took 1.85s to process
INFO 2025-07-20 17:47:12,143 basehttp 19892 18304 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-20 17:47:12,265 basehttp 19892 18304 "GET /api/admin/users/ HTTP/1.1" 200 1749
INFO 2025-07-20 17:47:12,476 basehttp 19892 18304 "GET /api/admin/users/8970a26f-bf93-43de-b044-508f76d5ea2a/ HTTP/1.1" 200 493
INFO 2025-07-20 17:47:12,559 basehttp 19892 18304 "GET /api/admin/users/?search=admin HTTP/1.1" 200 567
INFO 2025-07-20 17:47:12,657 basehttp 19892 18304 "GET /api/admin/users/?role=staff HTTP/1.1" 200 561
INFO 2025-07-20 17:47:12,683 basehttp 19892 18304 - Broken pipe from ('127.0.0.1', 4168)
INFO 2025-07-20 17:49:18,154 _client 19892 18572 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 17:49:18,160 supabase_client 19892 18572 Error authenticating user: Invalid API key
WARNING 2025-07-20 17:49:18,161 authentication 19892 18572 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 17:49:18,162 authentication 19892 18572 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 17:49:19,214 authentication 19892 18572 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 17:49:19,264 views 19892 18572 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 17:49:19,283 middleware 19892 18572 Slow request: POST /api/auth/login/ took 1.52s to process
INFO 2025-07-20 17:49:19,284 basehttp 19892 18572 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-20 17:49:19,366 basehttp 19892 18572 "GET /api/admin/users/ HTTP/1.1" 200 1749
INFO 2025-07-20 17:49:19,427 basehttp 19892 18572 "GET /api/admin/users/8970a26f-bf93-43de-b044-508f76d5ea2a/ HTTP/1.1" 200 493
INFO 2025-07-20 17:49:19,489 basehttp 19892 18572 "GET /api/admin/users/?search=admin HTTP/1.1" 200 567
INFO 2025-07-20 17:49:19,548 basehttp 19892 18572 "GET /api/admin/users/?role=staff HTTP/1.1" 200 561
INFO 2025-07-20 17:49:19,569 basehttp 19892 18572 - Broken pipe from ('127.0.0.1', 4290)
INFO 2025-07-20 18:19:11,872 _client 19892 6608 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 18:19:11,893 supabase_client 19892 6608 Error authenticating user: Invalid API key
WARNING 2025-07-20 18:19:11,897 authentication 19892 6608 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 18:19:11,898 authentication 19892 6608 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 18:19:13,055 authentication 19892 6608 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 18:19:13,135 views 19892 6608 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 18:19:13,168 middleware 19892 6608 Slow request: POST /api/auth/login/ took 1.82s to process
INFO 2025-07-20 18:19:13,171 basehttp 19892 6608 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-20 18:19:13,261 basehttp 19892 6608 "GET /api/admin/users/ HTTP/1.1" 200 1749
WARNING 2025-07-20 18:19:13,403 basehttp 19892 6608 "POST /api/admin/users/create/ HTTP/1.1" 400 201
INFO 2025-07-20 18:19:13,468 basehttp 19892 6608 "GET /api/admin/users/?search=admin HTTP/1.1" 200 567
INFO 2025-07-20 18:19:13,517 basehttp 19892 6608 "GET /api/admin/users/?role=admin HTTP/1.1" 200 176
INFO 2025-07-20 18:19:13,573 basehttp 19892 6608 "GET /api/admin/users/?page_size=2 HTTP/1.1" 200 951
WARNING 2025-07-20 18:19:13,636 basehttp 19892 6608 "POST /api/admin/users/ HTTP/1.1" 405 159
INFO 2025-07-20 18:19:13,659 basehttp 19892 6608 - Broken pipe from ('127.0.0.1', 5690)
INFO 2025-07-20 18:21:22,422 _client 19892 22748 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 18:21:22,426 supabase_client 19892 22748 Error authenticating user: Invalid API key
WARNING 2025-07-20 18:21:22,427 authentication 19892 22748 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 18:21:22,428 authentication 19892 22748 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 18:21:23,596 authentication 19892 22748 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 18:21:23,646 views 19892 22748 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 18:21:23,665 middleware 19892 22748 Slow request: POST /api/auth/login/ took 1.64s to process
INFO 2025-07-20 18:21:23,666 basehttp 19892 22748 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-20 18:21:23,762 basehttp 19892 22748 "GET /api/admin/users/ HTTP/1.1" 200 1749
WARNING 2025-07-20 18:21:24,955 middleware 19892 22748 Slow request: POST /api/admin/users/create/ took 1.19s to process
INFO 2025-07-20 18:21:24,962 basehttp 19892 22748 "POST /api/admin/users/create/ HTTP/1.1" 201 198
INFO 2025-07-20 18:21:25,024 basehttp 19892 22748 "GET /api/admin/users/?search=admin HTTP/1.1" 200 567
INFO 2025-07-20 18:21:25,085 basehttp 19892 22748 "GET /api/admin/users/?role=admin HTTP/1.1" 200 176
INFO 2025-07-20 18:21:25,154 basehttp 19892 22748 "GET /api/admin/users/?page_size=2 HTTP/1.1" 200 922
WARNING 2025-07-20 18:21:25,242 basehttp 19892 22748 "POST /api/admin/users/create/ HTTP/1.1" 400 306
INFO 2025-07-20 18:21:25,261 basehttp 19892 22748 - Broken pipe from ('127.0.0.1', 5809)
INFO 2025-07-20 18:22:23,397 _client 19892 20436 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 18:22:23,400 supabase_client 19892 20436 Error authenticating user: Invalid API key
WARNING 2025-07-20 18:22:23,401 authentication 19892 20436 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 18:22:23,403 authentication 19892 20436 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 18:22:24,659 authentication 19892 20436 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 18:22:24,712 views 19892 20436 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 18:22:24,728 middleware 19892 20436 Slow request: POST /api/auth/login/ took 1.41s to process
INFO 2025-07-20 18:22:24,730 basehttp 19892 20436 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-20 18:22:24,802 basehttp 19892 20436 "GET /api/admin/users/ HTTP/1.1" 200 2112
WARNING 2025-07-20 18:22:25,905 middleware 19892 20436 Slow request: POST /api/admin/users/create/ took 1.10s to process
INFO 2025-07-20 18:22:25,907 basehttp 19892 20436 "POST /api/admin/users/create/ HTTP/1.1" 201 198
INFO 2025-07-20 18:22:25,968 basehttp 19892 20436 "GET /api/admin/users/?search=admin HTTP/1.1" 200 567
INFO 2025-07-20 18:22:26,016 basehttp 19892 20436 "GET /api/admin/users/?role=admin HTTP/1.1" 200 176
INFO 2025-07-20 18:22:26,071 basehttp 19892 20436 "GET /api/admin/users/?page_size=2 HTTP/1.1" 200 899
WARNING 2025-07-20 18:22:26,129 basehttp 19892 20436 "POST /api/admin/users/create/ HTTP/1.1" 400 306
INFO 2025-07-20 18:22:26,147 basehttp 19892 20436 - Broken pipe from ('127.0.0.1', 5862)
INFO 2025-07-20 18:23:06,018 _client 19892 28544 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 18:23:06,021 supabase_client 19892 28544 Error authenticating user: Invalid API key
WARNING 2025-07-20 18:23:06,022 authentication 19892 28544 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 18:23:06,024 authentication 19892 28544 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 18:23:07,173 authentication 19892 28544 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 18:23:07,225 views 19892 28544 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 18:23:07,240 middleware 19892 28544 Slow request: POST /api/auth/login/ took 1.62s to process
INFO 2025-07-20 18:23:07,242 basehttp 19892 28544 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-20 18:23:07,317 basehttp 19892 28544 "GET /api/admin/users/ HTTP/1.1" 200 2475
WARNING 2025-07-20 18:23:08,427 middleware 19892 28544 Slow request: POST /api/admin/users/create/ took 1.10s to process
INFO 2025-07-20 18:23:08,429 basehttp 19892 28544 "POST /api/admin/users/create/ HTTP/1.1" 201 198
INFO 2025-07-20 18:23:08,494 basehttp 19892 28544 "GET /api/admin/users/?search=test.user.*************%40exobank.com HTTP/1.1" 200 538
INFO 2025-07-20 18:23:08,560 basehttp 19892 28544 "GET /api/admin/users/16e2415f-efb2-4229-8eba-ec97e0601861/ HTTP/1.1" 200 480
INFO 2025-07-20 18:23:08,669 basehttp 19892 28544 "PATCH /api/admin/users/16e2415f-efb2-4229-8eba-ec97e0601861/ HTTP/1.1" 200 159
INFO 2025-07-20 18:23:08,794 basehttp 19892 28544 "PATCH /api/admin/users/16e2415f-efb2-4229-8eba-ec97e0601861/ HTTP/1.1" 200 162
INFO 2025-07-20 18:23:08,850 basehttp 19892 28544 "GET /api/admin/users/?search=admin HTTP/1.1" 200 567
INFO 2025-07-20 18:23:08,901 basehttp 19892 28544 "GET /api/admin/users/?role=admin HTTP/1.1" 200 176
INFO 2025-07-20 18:23:08,960 basehttp 19892 28544 "GET /api/admin/users/?page_size=2 HTTP/1.1" 200 916
WARNING 2025-07-20 18:23:09,028 basehttp 19892 28544 "POST /api/admin/users/create/ HTTP/1.1" 400 306
INFO 2025-07-20 18:23:09,045 basehttp 19892 28544 - Broken pipe from ('127.0.0.1', 5905)
INFO 2025-07-20 18:24:54,743 _client 19892 25040 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-20 18:24:54,744 supabase_client 19892 25040 Error authenticating user: Invalid API key
WARNING 2025-07-20 18:24:54,745 authentication 19892 25040 Supabase authentication <NAME_EMAIL>
INFO 2025-07-20 18:24:54,746 authentication 19892 25040 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-20 18:24:55,819 authentication 19892 25040 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-20 18:24:55,871 views 19892 25040 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-20 18:24:55,889 middleware 19892 25040 Slow request: POST /api/auth/login/ took 1.54s to process
INFO 2025-07-20 18:24:55,891 basehttp 19892 25040 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-20 18:24:55,973 basehttp 19892 25040 "GET /api/admin/users/ HTTP/1.1" 200 2855
WARNING 2025-07-20 18:24:57,174 middleware 19892 25040 Slow request: POST /api/admin/users/create/ took 1.19s to process
INFO 2025-07-20 18:24:57,176 basehttp 19892 25040 "POST /api/admin/users/create/ HTTP/1.1" 201 198
INFO 2025-07-20 18:24:57,233 basehttp 19892 25040 "GET /api/admin/users/?search=test.user.*************%40exobank.com HTTP/1.1" 200 538
INFO 2025-07-20 18:24:57,293 basehttp 19892 25040 "GET /api/admin/users/25dc7757-c3a5-4197-a58b-62a98ed0220e/ HTTP/1.1" 200 480
INFO 2025-07-20 18:24:57,410 basehttp 19892 25040 "PATCH /api/admin/users/25dc7757-c3a5-4197-a58b-62a98ed0220e/ HTTP/1.1" 200 159
INFO 2025-07-20 18:24:57,473 basehttp 19892 25040 "GET /api/admin/users/?search=test.user.*************%40exobank.com HTTP/1.1" 200 552
INFO 2025-07-20 18:24:57,590 basehttp 19892 25040 "PATCH /api/admin/users/25dc7757-c3a5-4197-a58b-62a98ed0220e/ HTTP/1.1" 200 162
INFO 2025-07-20 18:24:57,655 basehttp 19892 25040 "GET /api/admin/users/?search=test.user.*************%40exobank.com HTTP/1.1" 200 555
INFO 2025-07-20 18:24:57,736 basehttp 19892 25040 "GET /api/admin/users/?search=admin HTTP/1.1" 200 567
INFO 2025-07-20 18:24:57,805 basehttp 19892 25040 "GET /api/admin/users/?role=admin HTTP/1.1" 200 176
INFO 2025-07-20 18:24:57,888 basehttp 19892 25040 "GET /api/admin/users/?page_size=2 HTTP/1.1" 200 933
WARNING 2025-07-20 18:24:57,953 basehttp 19892 25040 "POST /api/admin/users/create/ HTTP/1.1" 400 306
INFO 2025-07-20 18:24:57,969 basehttp 19892 25040 - Broken pipe from ('127.0.0.1', 5999)
INFO 2025-07-21 11:52:56,576 autoreload 28936 10472 Watching for file changes with StatReloader
INFO 2025-07-21 11:52:59,535 supabase_client 28936 10472 Supabase client initialized successfully for exobank project
INFO 2025-07-21 11:58:56,996 autoreload 13196 31128 Watching for file changes with StatReloader
INFO 2025-07-21 11:58:59,455 supabase_client 13196 31128 Supabase client initialized successfully for exobank project
INFO 2025-07-21 14:30:28,489 basehttp 13196 15224 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-21 14:30:30,922 _client 13196 15224 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-21 14:30:30,936 supabase_client 13196 15224 Error authenticating user: Invalid API key
WARNING 2025-07-21 14:30:30,938 authentication 13196 15224 Supabase authentication <NAME_EMAIL>
INFO 2025-07-21 14:30:30,938 authentication 13196 15224 Trying Django fallback <NAME_EMAIL>
WARNING 2025-07-21 14:30:33,592 authentication 13196 15224 Invalid password for <NAME_EMAIL>
WARNING 2025-07-21 14:30:37,000 views 13196 15224 Failed login attempt for email: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-21 14:30:37,048 performance 13196 15224 Slow API request: POST /api/auth/login/ took 8.4338s
WARNING 2025-07-21 14:30:37,048 middleware 13196 15224 Slow request: POST /api/auth/login/ took 8.43s to process
WARNING 2025-07-21 14:30:37,227 basehttp 13196 15224 "POST /api/auth/login/ HTTP/1.1" 401 172
INFO 2025-07-21 14:49:47,315 _client 13196 11632 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-21 14:49:47,336 supabase_client 13196 11632 Error authenticating user: Invalid API key
WARNING 2025-07-21 14:49:47,338 authentication 13196 11632 Supabase authentication <NAME_EMAIL>
INFO 2025-07-21 14:49:47,340 authentication 13196 11632 Trying Django fallback <NAME_EMAIL>
WARNING 2025-07-21 14:49:48,459 authentication 13196 11632 Invalid password for <NAME_EMAIL>
WARNING 2025-07-21 14:49:49,477 views 13196 11632 Failed login attempt for email: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-21 14:49:49,509 performance 13196 11632 Slow API request: POST /api/auth/login/ took 2.7640s
WARNING 2025-07-21 14:49:49,509 middleware 13196 11632 Slow request: POST /api/auth/login/ took 2.77s to process
WARNING 2025-07-21 14:49:49,560 basehttp 13196 11632 "POST /api/auth/login/ HTTP/1.1" 401 172
INFO 2025-07-21 14:50:04,683 _client 13196 11632 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-21 14:50:04,683 supabase_client 13196 11632 Error authenticating user: Invalid API key
WARNING 2025-07-21 14:50:04,683 authentication 13196 11632 Supabase authentication <NAME_EMAIL>
INFO 2025-07-21 14:50:04,683 authentication 13196 11632 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-21 14:50:06,264 authentication 13196 11632 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-21 14:50:06,385 views 13196 11632 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-21 14:50:06,428 middleware 13196 11632 Slow request: POST /api/auth/login/ took 1.80s to process
INFO 2025-07-21 14:50:06,597 basehttp 13196 11632 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-21 20:51:20,623 autoreload 20392 24864 Watching for file changes with StatReloader
INFO 2025-07-21 20:51:23,488 supabase_client 20392 24864 Supabase client initialized successfully for exobank project
INFO 2025-07-22 02:03:03,558 autoreload 7476 9020 Watching for file changes with StatReloader
INFO 2025-07-22 02:03:07,373 supabase_client 7476 9020 Supabase client initialized successfully for exobank project
INFO 2025-07-22 02:37:53,820 basehttp 7476 24296 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-22 02:37:55,938 _client 7476 24296 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/2 401 Unauthorized"
ERROR 2025-07-22 02:37:55,938 supabase_client 7476 24296 Error authenticating user: Invalid API key
WARNING 2025-07-22 02:37:55,938 authentication 7476 24296 Supabase authentication <NAME_EMAIL>
INFO 2025-07-22 02:37:55,938 authentication 7476 24296 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-22 02:37:57,154 authentication 7476 24296 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-22 02:37:57,252 views 7476 24296 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-22 02:37:57,302 performance 7476 24296 Slow API request: POST /api/auth/login/ took 3.4150s
WARNING 2025-07-22 02:37:57,302 middleware 7476 24296 Slow request: POST /api/auth/login/ took 3.42s to process
INFO 2025-07-22 02:37:57,442 basehttp 7476 24296 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-22 03:11:02,805 basehttp 7476 21968 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-22 03:11:03,544 _client 7476 21968 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/2 401 Unauthorized"
ERROR 2025-07-22 03:11:03,565 supabase_client 7476 21968 Error authenticating user: Invalid API key
WARNING 2025-07-22 03:11:03,569 authentication 7476 21968 Supabase authentication <NAME_EMAIL>
INFO 2025-07-22 03:11:03,571 authentication 7476 21968 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-22 03:11:05,626 authentication 7476 21968 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-22 03:11:05,729 views 7476 21968 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-22 03:11:05,772 performance 7476 21968 Slow API request: POST /api/auth/login/ took 2.9475s
WARNING 2025-07-22 03:11:05,777 middleware 7476 21968 Slow request: POST /api/auth/login/ took 2.95s to process
INFO 2025-07-22 03:11:06,018 basehttp 7476 21968 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-22 14:28:46,534 autoreload 1468 16088 Watching for file changes with StatReloader
INFO 2025-07-22 14:28:48,971 supabase_client 1468 16088 Supabase client initialized successfully for exobank project
INFO 2025-07-22 21:37:01,040 autoreload 26992 26192 Watching for file changes with StatReloader
INFO 2025-07-22 21:37:03,987 supabase_client 26992 26192 Supabase client initialized successfully for exobank project
INFO 2025-07-22 21:37:36,239 autoreload 21732 7228 Watching for file changes with StatReloader
INFO 2025-07-22 21:37:39,061 supabase_client 21732 7228 Supabase client initialized successfully for exobank project
INFO 2025-07-22 21:37:54,930 supabase_client 2528 11640 Supabase client initialized successfully for exobank project
INFO 2025-07-22 21:38:30,187 autoreload 26992 26192 C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\websockets\headers.py changed, reloading.
INFO 2025-07-22 21:38:39,350 autoreload 27516 25364 Watching for file changes with StatReloader
INFO 2025-07-22 21:38:45,116 supabase_client 27516 25364 Supabase client initialized successfully for exobank project
INFO 2025-07-22 21:39:00,825 autoreload 27516 25364 C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\h11\_readers.py changed, reloading.
INFO 2025-07-22 21:39:10,155 autoreload 14636 13384 Watching for file changes with StatReloader
INFO 2025-07-22 21:39:15,465 supabase_client 14636 13384 Supabase client initialized successfully for exobank project
INFO 2025-07-22 21:39:29,386 autoreload 14636 13384 C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\environ\environ.py changed, reloading.
INFO 2025-07-22 21:39:45,722 autoreload 20484 21804 Watching for file changes with StatReloader
INFO 2025-07-22 21:39:58,502 autoreload 20484 21804 C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_auth.py changed, reloading.
INFO 2025-07-22 21:40:07,473 autoreload 1772 14204 Watching for file changes with StatReloader
INFO 2025-07-22 21:40:09,584 supabase_client 1772 14204 Supabase client initialized successfully for exobank project
INFO 2025-07-22 21:40:22,439 autoreload 7792 19056 Watching for file changes with StatReloader
INFO 2025-07-22 21:40:24,597 supabase_client 7792 19056 Supabase client initialized successfully for exobank project
INFO 2025-07-22 21:41:05,326 supabase_client 2200 12672 Supabase client initialized successfully for exobank project
INFO 2025-07-22 22:24:22,177 autoreload 27804 31956 Watching for file changes with StatReloader
INFO 2025-07-22 22:24:24,018 supabase_client 27804 31956 Supabase client initialized successfully for exobank project
INFO 2025-07-22 22:25:18,472 supabase_client 29528 22660 Supabase client initialized successfully for exobank project
INFO 2025-07-22 22:25:35,506 autoreload 15280 27548 Watching for file changes with StatReloader
INFO 2025-07-22 22:25:37,466 supabase_client 15280 27548 Supabase client initialized successfully for exobank project
INFO 2025-07-22 22:26:47,632 autoreload 20656 26432 Watching for file changes with StatReloader
INFO 2025-07-22 22:26:51,718 supabase_client 20656 26432 Supabase client initialized successfully for exobank project
INFO 2025-07-22 22:35:23,478 basehttp 20656 19696 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-22 22:35:25,343 _client 20656 19696 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-22 22:35:25,343 supabase_client 20656 19696 Error authenticating user: Invalid API key
WARNING 2025-07-22 22:35:25,343 authentication 20656 19696 Supabase authentication <NAME_EMAIL>
INFO 2025-07-22 22:35:25,343 authentication 20656 19696 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-22 22:35:26,754 authentication 20656 19696 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-22 22:35:26,863 views 20656 19696 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-22 22:35:26,909 performance 20656 19696 Slow API request: POST /api/auth/login/ took 3.3813s
WARNING 2025-07-22 22:35:26,909 middleware 20656 19696 Slow request: POST /api/auth/login/ took 3.38s to process
INFO 2025-07-22 22:35:27,057 basehttp 20656 19696 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-22 22:35:49,311 _client 20656 28764 HTTP Request: POST https://wmlsydbfwhevuaavjkje.supabase.co/auth/v1/token?grant_type=password "HTTP/1.1 401 Unauthorized"
ERROR 2025-07-22 22:35:49,315 supabase_client 20656 28764 Error authenticating user: Invalid API key
WARNING 2025-07-22 22:35:49,316 authentication 20656 28764 Supabase authentication <NAME_EMAIL>
INFO 2025-07-22 22:35:49,317 authentication 20656 28764 Trying Django fallback <NAME_EMAIL>
INFO 2025-07-22 22:35:52,642 authentication 20656 28764 Django fallback authentication <NAME_EMAIL>
INFO 2025-07-22 22:35:52,744 views 20656 28764 User logged in: <EMAIL> from IP: 127.0.0.1
WARNING 2025-07-22 22:35:52,784 performance 20656 28764 Slow API request: POST /api/auth/login/ took 3.5824s
WARNING 2025-07-22 22:35:52,785 middleware 20656 28764 Slow request: POST /api/auth/login/ took 3.59s to process
INFO 2025-07-22 22:35:52,983 basehttp 20656 28764 "POST /api/auth/login/ HTTP/1.1" 200 1352
INFO 2025-07-23 01:52:02,651 supabase_client 27656 13076 Supabase client initialized successfully for exobank project
INFO 2025-07-23 01:52:28,415 supabase_client 26304 13756 Supabase client initialized successfully for exobank project
INFO 2025-07-23 02:16:16,005 supabase_client 24792 29912 Supabase client initialized successfully for exobank project
INFO 2025-07-23 02:16:41,442 supabase_client 1596 28828 Supabase client initialized successfully for exobank project
INFO 2025-07-23 02:17:13,015 supabase_client 32696 19000 Supabase client initialized successfully for exobank project
INFO 2025-07-23 02:17:39,800 supabase_client 17020 28844 Supabase client initialized successfully for exobank project
