/**
 * Real Admin API Service for ExoBank Admin Frontend
 * 
 * This service provides real HTTP requests to Django admin endpoints,
 * replacing the mock API service with actual backend integration.
 */

import {
  User,
  AdminUser,
  DashboardStats,
  AuditEvent,
  SecurityEvent,
  LoginAttempt,
  Account,
  Transaction,
  Beneficiary,
  UserCreateRequest,
  UserUpdateRequest,
  UserDeactivateRequest,
  SecurityEventUpdateRequest,
  AccountCreateRequest,
  AccountUpdateRequest,
  AccountStatusUpdateRequest,
  AccountBalanceUpdateRequest,
  UserFilters,
  AuditLogFilters,
  SecurityEventFilters,
  LoginAttemptFilters,
  ApiResponse,
  PaginatedResponse,
  HealthCheckResponse
} from './types'

import { getAuthData, refreshToken, clearAuthData, ApiError } from './auth'

// Environment configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'
const API_TIMEOUT = parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || '10000')
const RETRY_ATTEMPTS = parseInt(process.env.NEXT_PUBLIC_RETRY_ATTEMPTS || '3')

/**
 * Admin API Service Class
 */
class AdminApiService {
  private baseURL: string
  private timeout: number
  private retryAttempts: number

  constructor() {
    this.baseURL = API_BASE_URL
    this.timeout = API_TIMEOUT
    this.retryAttempts = RETRY_ATTEMPTS
  }

  /**
   * Make authenticated API request with retry logic
   */
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
    retryCount = 0
  ): Promise<ApiResponse<T>> {
    const auth = getAuthData()
    
    if (!auth.access) {
      throw new ApiError('No access token available')
    }

    const url = `${this.baseURL}/api/${endpoint}`
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${auth.access}`,
      ...options.headers
    }

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.timeout)

    try {
      const response = await fetch(url, {
        ...options,
        headers,
        signal: controller.signal
      })

      clearTimeout(timeoutId)
      const data = await response.json()

      if (!response.ok) {
        // Handle token expiration
        if (response.status === 401 && auth.refresh && retryCount === 0) {
          try {
            await refreshToken()
            return this.makeRequest<T>(endpoint, options, retryCount + 1)
          } catch (refreshError) {
            clearAuthData()
            throw new ApiError('Authentication expired. Please log in again.')
          }
        }

        const error = new ApiError(data.error?.message || 'Request failed')
        error.code = data.error?.code
        error.status = response.status
        error.details = data.error?.details
        throw error
      }

      return data
    } catch (error) {
      clearTimeout(timeoutId)

      if (error instanceof ApiError) {
        throw error
      }

      // Retry on network errors
      if (retryCount < this.retryAttempts && (
        error.name === 'AbortError' || 
        error.name === 'TypeError' ||
        error.message.includes('fetch')
      )) {
        await this.delay(1000 * (retryCount + 1)) // Exponential backoff
        return this.makeRequest<T>(endpoint, options, retryCount + 1)
      }

      const apiError = new ApiError('Network error occurred')
      apiError.code = 'NETWORK_ERROR'
      throw apiError
    }
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Build query string from filters
   */
  private buildQueryString(filters: Record<string, any>): string {
    const params = new URLSearchParams()
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, String(value))
      }
    })
    
    return params.toString()
  }

  // Authentication Methods
  async login(email: string, password: string): Promise<ApiResponse<{ access: string; refresh: string; user: User }>> {
    const response = await fetch(`${this.baseURL}/api/auth/login/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email, password })
    })

    return response.json()
  }

  async logout(): Promise<ApiResponse<{}>> {
    const auth = getAuthData()
    return this.makeRequest('auth/logout/', {
      method: 'POST',
      body: JSON.stringify({ refresh: auth.refresh })
    })
  }

  // Dashboard Methods
  async getDashboardStats(): Promise<ApiResponse<DashboardStats>> {
    return this.makeRequest<DashboardStats>('admin/dashboard/stats/')
  }

  // User Management Methods
  async getUsers(filters: UserFilters = {}): Promise<ApiResponse<PaginatedResponse<AdminUser>>> {
    const queryString = this.buildQueryString(filters)
    const endpoint = queryString ? `admin/users/?${queryString}` : 'admin/users/'
    return this.makeRequest<PaginatedResponse<AdminUser>>(endpoint)
  }

  async getUser(userId: string): Promise<ApiResponse<AdminUser>> {
    return this.makeRequest<AdminUser>(`admin/users/${userId}/`)
  }

  async createUser(userData: UserCreateRequest): Promise<ApiResponse<AdminUser>> {
    return this.makeRequest<AdminUser>('admin/users/', {
      method: 'POST',
      body: JSON.stringify(userData)
    })
  }

  async updateUser(userId: string, userData: UserUpdateRequest): Promise<ApiResponse<AdminUser>> {
    return this.makeRequest<AdminUser>(`admin/users/${userId}/`, {
      method: 'PATCH',
      body: JSON.stringify(userData)
    })
  }

  async deactivateUser(userId: string, data: UserDeactivateRequest): Promise<ApiResponse<AdminUser>> {
    return this.makeRequest<AdminUser>(`admin/users/${userId}/`, {
      method: 'PATCH',
      body: JSON.stringify(data)
    })
  }

  // Audit Log Methods
  async getAuditLogs(filters: AuditLogFilters = {}): Promise<ApiResponse<PaginatedResponse<AuditEvent>>> {
    const queryString = this.buildQueryString(filters)
    const endpoint = queryString ? `admin/audit/logs/?${queryString}` : 'admin/audit/logs/'
    return this.makeRequest<PaginatedResponse<AuditEvent>>(endpoint)
  }

  // Security Methods
  async getSecurityAlerts(filters: SecurityEventFilters = {}): Promise<ApiResponse<PaginatedResponse<SecurityEvent>>> {
    const queryString = this.buildQueryString(filters)
    const endpoint = queryString ? `admin/security/alerts/?${queryString}` : 'admin/security/alerts/'
    return this.makeRequest<PaginatedResponse<SecurityEvent>>(endpoint)
  }

  async getSecurityAlert(alertId: string): Promise<ApiResponse<SecurityEvent>> {
    return this.makeRequest<SecurityEvent>(`admin/security/alerts/${alertId}/`)
  }

  async updateSecurityAlert(alertId: string, data: SecurityEventUpdateRequest): Promise<ApiResponse<SecurityEvent>> {
    return this.makeRequest<SecurityEvent>(`admin/security/alerts/${alertId}/`, {
      method: 'PATCH',
      body: JSON.stringify(data)
    })
  }

  async getLoginAttempts(filters: LoginAttemptFilters = {}): Promise<ApiResponse<PaginatedResponse<LoginAttempt>>> {
    const queryString = this.buildQueryString(filters)
    const endpoint = queryString ? `admin/security/login-attempts/?${queryString}` : 'admin/security/login-attempts/'
    return this.makeRequest<PaginatedResponse<LoginAttempt>>(endpoint)
  }

  // Banking Data Methods (for admin oversight)
  async getAccounts(filters: Record<string, any> = {}): Promise<ApiResponse<PaginatedResponse<Account>>> {
    const queryString = this.buildQueryString(filters)
    const endpoint = queryString ? `accounts/?${queryString}` : 'accounts/'
    return this.makeRequest<PaginatedResponse<Account>>(endpoint)
  }

  async getAccount(accountId: string): Promise<ApiResponse<Account>> {
    return this.makeRequest<Account>(`accounts/${accountId}/`)
  }

  async createAccount(accountData: AccountCreateRequest): Promise<ApiResponse<Account>> {
    return this.makeRequest<Account>('accounts/create/', {
      method: 'POST',
      body: JSON.stringify(accountData)
    })
  }

  async updateAccount(accountId: string, updateData: AccountUpdateRequest): Promise<ApiResponse<Account>> {
    return this.makeRequest<Account>(`accounts/${accountId}/`, {
      method: 'PATCH',
      body: JSON.stringify(updateData)
    })
  }

  async updateAccountStatus(accountId: string, statusData: AccountStatusUpdateRequest): Promise<ApiResponse<Account>> {
    return this.makeRequest<Account>(`accounts/${accountId}/status/`, {
      method: 'PATCH',
      body: JSON.stringify(statusData)
    })
  }

  async updateAccountBalance(accountId: string, balanceData: AccountBalanceUpdateRequest): Promise<ApiResponse<Account>> {
    return this.makeRequest<Account>(`accounts/${accountId}/balance/`, {
      method: 'PATCH',
      body: JSON.stringify(balanceData)
    })
  }

  async closeAccount(accountId: string, reason?: string): Promise<ApiResponse<Account>> {
    return this.makeRequest<Account>(`accounts/${accountId}/close/`, {
      method: 'PATCH',
      body: JSON.stringify({ reason })
    })
  }

  async getTransactions(filters: Record<string, any> = {}): Promise<ApiResponse<PaginatedResponse<Transaction>>> {
    const queryString = this.buildQueryString(filters)
    const endpoint = queryString ? `transactions/?${queryString}` : 'transactions/'
    return this.makeRequest<PaginatedResponse<Transaction>>(endpoint)
  }

  async getBeneficiaries(filters: Record<string, any> = {}): Promise<ApiResponse<PaginatedResponse<Beneficiary>>> {
    const queryString = this.buildQueryString(filters)
    const endpoint = queryString ? `accounts/beneficiaries/?${queryString}` : 'accounts/beneficiaries/'
    return this.makeRequest<PaginatedResponse<Beneficiary>>(endpoint)
  }

  // System Health Methods
  async getHealthCheck(): Promise<ApiResponse<HealthCheckResponse>> {
    return this.makeRequest<HealthCheckResponse>('admin/health/')
  }

  // Real-time Data Methods
  async getRealtimeStats(): Promise<ApiResponse<DashboardStats>> {
    // This could be enhanced with WebSocket connections for real-time updates
    return this.getDashboardStats()
  }

  // Report Generation Methods
  async generateUserReport(filters: Record<string, any> = {}): Promise<ApiResponse<Blob>> {
    const queryString = this.buildQueryString({ ...filters, format: 'csv' })
    const endpoint = `admin/reports/users/?${queryString}`
    
    const response = await this.makeRequest<any>(endpoint, {
      headers: {
        'Accept': 'text/csv'
      }
    })
    
    return response
  }

  async generateTransactionReport(filters: Record<string, any> = {}): Promise<ApiResponse<Blob>> {
    const queryString = this.buildQueryString({ ...filters, format: 'csv' })
    const endpoint = `admin/reports/transactions/?${queryString}`
    
    const response = await this.makeRequest<any>(endpoint, {
      headers: {
        'Accept': 'text/csv'
      }
    })
    
    return response
  }

  async generateAuditReport(filters: Record<string, any> = {}): Promise<ApiResponse<Blob>> {
    const queryString = this.buildQueryString({ ...filters, format: 'csv' })
    const endpoint = `admin/reports/audit/?${queryString}`
    
    const response = await this.makeRequest<any>(endpoint, {
      headers: {
        'Accept': 'text/csv'
      }
    })
    
    return response
  }

  // Utility Methods
  isHealthy(): boolean {
    // Simple health check - could be enhanced with actual API call
    return true
  }

  getApiVersion(): string {
    return process.env.NEXT_PUBLIC_API_VERSION || 'v1'
  }

  getBaseUrl(): string {
    return this.baseURL
  }
}

// Create and export singleton instance
const adminApiService = new AdminApiService()

export default adminApiService
export { AdminApiService }