/**
 * Shared Authentication Types for ExoBank Frontend Applications
 * 
 * Common types and interfaces used across React Native and Next.js applications
 * for consistent authentication handling.
 */

/**
 * User roles in the 5-tier hierarchy
 */
export enum UserRole {
  CUSTOMER = 'customer',
  STAFF = 'staff',
  MANAGER = 'manager',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin',
}

/**
 * User status types
 */
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING = 'pending',
}

/**
 * Authentication user data structure
 */
export interface AuthUser {
  id: string;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  phone_number?: string;
  role: UserRole;
  status: UserStatus;
  is_verified: boolean;
  created_at: string;
  updated_at: string;
  last_login?: string;
  profile_picture?: string;
  
  // Role-based properties
  is_staff: boolean;
  is_manager: boolean;
  is_admin: boolean;
  is_super_admin: boolean;
  
  // Computed properties
  full_name: string;
  role_display: string;
}

/**
 * Authentication session data
 */
export interface AuthSession {
  user: AuthUser;
  access_token: string;
  refresh_token: string;
  expires_at?: number;
  created_at: number;
}

/**
 * Authentication response from API
 */
export interface AuthResponse {
  user: AuthUser | null;
  error: AuthError | null;
}

/**
 * Login response from backend
 */
export interface LoginResponse {
  user: AuthUser;
  access: string;
  refresh: string;
  message: string;
}

/**
 * Authentication error structure
 */
export interface AuthError {
  code: string;
  message: string;
  details?: any;
}

/**
 * Login request data
 */
export interface LoginRequest {
  email: string;
  password: string;
}

/**
 * Registration request data
 */
export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  password_confirm: string;
  first_name: string;
  last_name: string;
  phone_number?: string;
}

/**
 * Password change request
 */
export interface PasswordChangeRequest {
  current_password: string;
  new_password: string;
  new_password_confirm: string;
}

/**
 * Password reset request
 */
export interface PasswordResetRequest {
  email: string;
}

/**
 * Token refresh response
 */
export interface TokenRefreshResponse {
  access: string;
  refresh?: string;
}

/**
 * Authentication state change events
 */
export enum AuthEvent {
  SIGNED_IN = 'SIGNED_IN',
  SIGNED_OUT = 'SIGNED_OUT',
  TOKEN_REFRESHED = 'TOKEN_REFRESHED',
  USER_UPDATED = 'USER_UPDATED',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
}

/**
 * Authentication state change callback
 */
export type AuthStateChangeCallback = (event: AuthEvent, session: AuthSession | null) => void;

/**
 * Authentication subscription
 */
export interface AuthSubscription {
  unsubscribe: () => void;
}

/**
 * Storage interface for authentication data
 */
export interface AuthStorage {
  getToken(): Promise<string | null>;
  setToken(token: string): Promise<void>;
  getRefreshToken(): Promise<string | null>;
  setRefreshToken(token: string): Promise<void>;
  getUserData(): Promise<AuthUser | null>;
  setUserData(user: AuthUser): Promise<void>;
  clearAll(): Promise<void>;
}

/**
 * Authentication service interface
 */
export interface AuthService {
  // Core authentication methods
  signIn(email: string, password: string): Promise<AuthResponse>;
  signUp(email: string, password: string, metadata?: any): Promise<AuthResponse>;
  signOut(): Promise<{ error: AuthError | null }>;
  
  // User management
  getUser(): Promise<AuthUser | null>;
  updateUser(updates: Partial<AuthUser>): Promise<AuthResponse>;
  
  // Session management
  getSession(): Promise<{ data: { session: AuthSession | null } }>;
  setSession(session: AuthSession | null): Promise<{ error: AuthError | null }>;
  refreshSession(): Promise<{ data: { session: AuthSession | null }; error: AuthError | null }>;
  
  // Token management
  getAccessToken(): Promise<string | null>;
  refreshToken(): Promise<{ data: { session: AuthSession | null }; error: AuthError | null }>;
  
  // Password management
  resetPasswordForEmail(email: string): Promise<{ data: any; error: AuthError | null }>;
  updatePassword(password: string): Promise<{ error: AuthError | null }>;
  
  // State management
  onAuthStateChange(callback: AuthStateChangeCallback): { data: { subscription: AuthSubscription } };
  isAuthenticated(): Promise<boolean>;
}

/**
 * Role-based access control helpers
 */
export const RoleHierarchy = {
  [UserRole.CUSTOMER]: 1,
  [UserRole.STAFF]: 2,
  [UserRole.MANAGER]: 3,
  [UserRole.ADMIN]: 4,
  [UserRole.SUPER_ADMIN]: 5,
} as const;

/**
 * Check if user has required role or higher
 */
export function hasRoleOrAbove(userRole: UserRole, requiredRole: UserRole): boolean {
  return RoleHierarchy[userRole] >= RoleHierarchy[requiredRole];
}

/**
 * Check if user has admin access (staff or above)
 */
export function hasAdminAccess(userRole: UserRole): boolean {
  return hasRoleOrAbove(userRole, UserRole.STAFF);
}

/**
 * Check if user has manager access (manager or above)
 */
export function hasManagerAccess(userRole: UserRole): boolean {
  return hasRoleOrAbove(userRole, UserRole.MANAGER);
}

/**
 * Check if user can manage other users
 */
export function canManageUsers(userRole: UserRole): boolean {
  return hasRoleOrAbove(userRole, UserRole.MANAGER);
}

/**
 * Get display name for role
 */
export function getRoleDisplayName(role: UserRole): string {
  const roleNames = {
    [UserRole.CUSTOMER]: 'Customer',
    [UserRole.STAFF]: 'Staff',
    [UserRole.MANAGER]: 'Manager',
    [UserRole.ADMIN]: 'Admin',
    [UserRole.SUPER_ADMIN]: 'Super Admin',
  };
  
  return roleNames[role] || 'Unknown';
}

/**
 * Authentication error codes
 */
export enum AuthErrorCode {
  // Authentication errors
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  ACCOUNT_INACTIVE = 'ACCOUNT_INACTIVE',
  ACCOUNT_SUSPENDED = 'ACCOUNT_SUSPENDED',
  ACCOUNT_PENDING = 'ACCOUNT_PENDING',
  
  // Token errors
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_INVALID = 'TOKEN_INVALID',
  TOKEN_REFRESH_FAILED = 'TOKEN_REFRESH_FAILED',
  NO_TOKEN = 'NO_TOKEN',
  
  // Permission errors
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  ADMIN_ACCESS_REQUIRED = 'ADMIN_ACCESS_REQUIRED',
  
  // Network errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  
  // Validation errors
  INVALID_EMAIL = 'INVALID_EMAIL',
  WEAK_PASSWORD = 'WEAK_PASSWORD',
  PASSWORD_MISMATCH = 'PASSWORD_MISMATCH',
  
  // Registration errors
  EMAIL_ALREADY_EXISTS = 'EMAIL_ALREADY_EXISTS',
  USERNAME_ALREADY_EXISTS = 'USERNAME_ALREADY_EXISTS',
  
  // General errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

/**
 * Create standardized auth error
 */
export function createAuthError(code: AuthErrorCode, message: string, details?: any): AuthError {
  return {
    code,
    message,
    details,
  };
}

/**
 * Authentication configuration
 */
export const AUTH_CONFIG = {
  // Token refresh threshold (5 minutes before expiration)
  TOKEN_REFRESH_THRESHOLD_MINUTES: 5,
  
  // Session timeout (24 hours of inactivity)
  SESSION_TIMEOUT_HOURS: 24,
  
  // Maximum login attempts
  MAX_LOGIN_ATTEMPTS: 5,
  
  // Password requirements
  MIN_PASSWORD_LENGTH: 8,
  
  // API endpoints
  ENDPOINTS: {
    LOGIN: '/api/auth/login/',
    REGISTER: '/api/auth/register/',
    LOGOUT: '/api/auth/logout/',
    REFRESH: '/api/auth/token/refresh/',
    PROFILE: '/api/auth/profile/',
    PASSWORD_CHANGE: '/api/auth/password/change/',
    PASSWORD_RESET: '/api/auth/password/reset/',
    ADMIN_ACCESS: '/api/auth/access/admin-dashboard/',
  },
} as const;
