/**
 * Security Utilities for ExoBank Admin Frontend
 * 
 * Enhanced security features including secure storage, session management,
 * and security monitoring for the Next.js admin application.
 */

/**
 * Security configuration
 */
export const SECURITY_CONFIG = {
  // Session timeout (24 hours of inactivity)
  SESSION_TIMEOUT_MS: 24 * 60 * 60 * 1000,
  
  // Maximum failed login attempts before lockout
  MAX_FAILED_ATTEMPTS: 5,
  
  // Lockout duration (15 minutes)
  LOCKOUT_DURATION_MS: 15 * 60 * 1000,
  
  // Token refresh threshold (5 minutes before expiration)
  TOKEN_REFRESH_THRESHOLD_MS: 5 * 60 * 1000,
  
  // Security event logging
  ENABLE_SECURITY_LOGGING: true,
} as const;

/**
 * Security event types
 */
export enum SecurityEvent {
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILED = 'LOGIN_FAILED',
  LOGOUT = 'LOGOUT',
  TOKEN_REFRESHED = 'TOKEN_REFRESHED',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  SESSION_TIMEOUT = 'SESSION_TIMEOUT',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  UNAUTHORIZED_ACCESS = 'UNAUTHORIZED_ACCESS',
}

/**
 * Security event data
 */
export interface SecurityEventData {
  event: SecurityEvent;
  timestamp: number;
  userId?: string;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Failed login attempt tracking
 */
interface FailedAttempt {
  email: string;
  attempts: number;
  lastAttempt: number;
  lockedUntil?: number;
}

/**
 * Session metadata for security tracking
 */
export interface SessionMetadata {
  createdAt: number;
  lastActivity: number;
  ipAddress?: string;
  userAgent?: string;
  tabId?: string;
}

/**
 * Secure storage manager for browser environment
 */
export class SecureStorage {
  private static readonly STORAGE_PREFIX = 'exobank_admin_secure_';
  
  /**
   * Store data securely in localStorage
   */
  static setSecureItem(key: string, value: string): void {
    try {
      if (typeof window === 'undefined') return;
      
      const secureKey = this.STORAGE_PREFIX + key;
      // In a production app, you would encrypt the value here
      localStorage.setItem(secureKey, value);
    } catch (error) {
      console.error('Error storing secure item:', error);
      throw new Error('Failed to store secure data');
    }
  }
  
  /**
   * Retrieve data securely from localStorage
   */
  static getSecureItem(key: string): string | null {
    try {
      if (typeof window === 'undefined') return null;
      
      const secureKey = this.STORAGE_PREFIX + key;
      const value = localStorage.getItem(secureKey);
      // In a production app, you would decrypt the value here
      return value;
    } catch (error) {
      console.error('Error retrieving secure item:', error);
      return null;
    }
  }
  
  /**
   * Remove secure item
   */
  static removeSecureItem(key: string): void {
    try {
      if (typeof window === 'undefined') return;
      
      const secureKey = this.STORAGE_PREFIX + key;
      localStorage.removeItem(secureKey);
    } catch (error) {
      console.error('Error removing secure item:', error);
    }
  }
  
  /**
   * Clear all secure items
   */
  static clearAllSecureItems(): void {
    try {
      if (typeof window === 'undefined') return;
      
      const keys = Object.keys(localStorage);
      const secureKeys = keys.filter(key => key.startsWith(this.STORAGE_PREFIX));
      secureKeys.forEach(key => localStorage.removeItem(key));
    } catch (error) {
      console.error('Error clearing secure items:', error);
    }
  }
}

/**
 * Security event logger
 */
export class SecurityLogger {
  private static events: SecurityEventData[] = [];
  private static readonly MAX_EVENTS = 100;
  
  /**
   * Log security event
   */
  static logEvent(event: SecurityEvent, details?: any, userId?: string): void {
    if (!SECURITY_CONFIG.ENABLE_SECURITY_LOGGING) {
      return;
    }
    
    const eventData: SecurityEventData = {
      event,
      timestamp: Date.now(),
      userId,
      details,
      ipAddress: this.getClientIP(),
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'unknown',
    };
    
    this.events.push(eventData);
    
    // Keep only the last MAX_EVENTS
    if (this.events.length > this.MAX_EVENTS) {
      this.events = this.events.slice(-this.MAX_EVENTS);
    }
    
    // Store events persistently
    try {
      SecureStorage.setSecureItem('security_events', JSON.stringify(this.events));
    } catch (error) {
      console.error('Error storing security events:', error);
    }
    
    // In a production app, you would also send critical events to a security monitoring service
    if (this.isCriticalEvent(event)) {
      console.warn('Critical security event:', eventData);
    }
  }
  
  /**
   * Get security events
   */
  static getEvents(): SecurityEventData[] {
    try {
      const eventsJson = SecureStorage.getSecureItem('security_events');
      if (eventsJson) {
        this.events = JSON.parse(eventsJson);
      }
      return [...this.events];
    } catch (error) {
      console.error('Error retrieving security events:', error);
      return [];
    }
  }
  
  /**
   * Clear security events
   */
  static clearEvents(): void {
    this.events = [];
    SecureStorage.removeSecureItem('security_events');
  }
  
  /**
   * Check if event is critical
   */
  private static isCriticalEvent(event: SecurityEvent): boolean {
    return [
      SecurityEvent.SUSPICIOUS_ACTIVITY,
      SecurityEvent.TOKEN_EXPIRED,
      SecurityEvent.SESSION_TIMEOUT,
      SecurityEvent.UNAUTHORIZED_ACCESS,
    ].includes(event);
  }
  
  /**
   * Get client IP (simplified for demo)
   */
  private static getClientIP(): string {
    // In a real application, you would get the actual client IP
    return 'unknown';
  }
}

/**
 * Failed login attempt manager
 */
export class FailedLoginManager {
  private static readonly STORAGE_KEY = 'failed_attempts';
  
  /**
   * Record failed login attempt
   */
  static recordFailedAttempt(email: string): boolean {
    try {
      const attempts = this.getFailedAttempts();
      const now = Date.now();
      
      let attempt = attempts.find(a => a.email === email);
      
      if (!attempt) {
        attempt = {
          email,
          attempts: 0,
          lastAttempt: now,
        };
        attempts.push(attempt);
      }
      
      // Check if still locked
      if (attempt.lockedUntil && now < attempt.lockedUntil) {
        return false; // Still locked
      }
      
      // Reset if lockout period has passed
      if (attempt.lockedUntil && now >= attempt.lockedUntil) {
        attempt.attempts = 0;
        attempt.lockedUntil = undefined;
      }
      
      attempt.attempts++;
      attempt.lastAttempt = now;
      
      // Lock account if max attempts reached
      if (attempt.attempts >= SECURITY_CONFIG.MAX_FAILED_ATTEMPTS) {
        attempt.lockedUntil = now + SECURITY_CONFIG.LOCKOUT_DURATION_MS;
        
        SecurityLogger.logEvent(SecurityEvent.SUSPICIOUS_ACTIVITY, {
          reason: 'Max failed login attempts reached',
          email,
          attempts: attempt.attempts,
        });
      }
      
      SecureStorage.setSecureItem(this.STORAGE_KEY, JSON.stringify(attempts));
      
      return attempt.lockedUntil ? false : true; // Return false if locked
    } catch (error) {
      console.error('Error recording failed attempt:', error);
      return true; // Allow attempt on error
    }
  }
  
  /**
   * Clear failed attempts for email
   */
  static clearFailedAttempts(email: string): void {
    try {
      const attempts = this.getFailedAttempts();
      const filtered = attempts.filter(a => a.email !== email);
      SecureStorage.setSecureItem(this.STORAGE_KEY, JSON.stringify(filtered));
    } catch (error) {
      console.error('Error clearing failed attempts:', error);
    }
  }
  
  /**
   * Check if email is locked
   */
  static isLocked(email: string): boolean {
    try {
      const attempts = this.getFailedAttempts();
      const attempt = attempts.find(a => a.email === email);
      
      if (!attempt || !attempt.lockedUntil) {
        return false;
      }
      
      const now = Date.now();
      return now < attempt.lockedUntil;
    } catch (error) {
      console.error('Error checking lock status:', error);
      return false;
    }
  }
  
  /**
   * Get failed attempts
   */
  private static getFailedAttempts(): FailedAttempt[] {
    try {
      const attemptsJson = SecureStorage.getSecureItem(this.STORAGE_KEY);
      return attemptsJson ? JSON.parse(attemptsJson) : [];
    } catch (error) {
      console.error('Error getting failed attempts:', error);
      return [];
    }
  }
}

/**
 * Session manager for enhanced session security in browser environment
 */
export class SessionManager {
  private static readonly METADATA_KEY = 'session_metadata';
  private static sessionTimeout: NodeJS.Timeout | null = null;

  /**
   * Create session with metadata
   */
  static createSession(userId: string, metadata?: Partial<SessionMetadata>): void {
    const sessionMetadata: SessionMetadata = {
      createdAt: Date.now(),
      lastActivity: Date.now(),
      ipAddress: metadata?.ipAddress || 'unknown',
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'unknown',
      tabId: metadata?.tabId || this.generateTabId(),
    };

    try {
      SecureStorage.setSecureItem(this.METADATA_KEY, JSON.stringify(sessionMetadata));

      // Start session timeout monitoring
      this.startSessionTimeout();

      SecurityLogger.logEvent(SecurityEvent.LOGIN_SUCCESS, {
        userId,
        metadata: sessionMetadata,
      }, userId);
    } catch (error) {
      console.error('Error creating session:', error);
    }
  }

  /**
   * Update session activity
   */
  static updateActivity(): void {
    try {
      const metadata = this.getSessionMetadata();
      if (metadata) {
        metadata.lastActivity = Date.now();
        SecureStorage.setSecureItem(this.METADATA_KEY, JSON.stringify(metadata));

        // Reset session timeout
        this.startSessionTimeout();
      }
    } catch (error) {
      console.error('Error updating session activity:', error);
    }
  }

  /**
   * Check if session is valid (not timed out)
   */
  static isSessionValid(): boolean {
    try {
      const metadata = this.getSessionMetadata();
      if (!metadata) {
        return false;
      }

      const now = Date.now();
      const timeSinceLastActivity = now - metadata.lastActivity;

      return timeSinceLastActivity < SECURITY_CONFIG.SESSION_TIMEOUT_MS;
    } catch (error) {
      console.error('Error checking session validity:', error);
      return false;
    }
  }

  /**
   * Clear session
   */
  static clearSession(): void {
    try {
      SecureStorage.removeSecureItem(this.METADATA_KEY);

      if (this.sessionTimeout) {
        clearTimeout(this.sessionTimeout);
        this.sessionTimeout = null;
      }

      SecurityLogger.logEvent(SecurityEvent.LOGOUT);
    } catch (error) {
      console.error('Error clearing session:', error);
    }
  }

  /**
   * Get session metadata
   */
  static getSessionMetadata(): SessionMetadata | null {
    try {
      const metadataJson = SecureStorage.getSecureItem(this.METADATA_KEY);
      return metadataJson ? JSON.parse(metadataJson) : null;
    } catch (error) {
      console.error('Error getting session metadata:', error);
      return null;
    }
  }

  /**
   * Start session timeout monitoring
   */
  private static startSessionTimeout(): void {
    if (this.sessionTimeout) {
      clearTimeout(this.sessionTimeout);
    }

    this.sessionTimeout = setTimeout(() => {
      SecurityLogger.logEvent(SecurityEvent.SESSION_TIMEOUT);
      // In a real app, you would trigger a logout here
      console.warn('Session timeout detected');
    }, SECURITY_CONFIG.SESSION_TIMEOUT_MS);
  }

  /**
   * Generate unique tab ID
   */
  private static generateTabId(): string {
    return Math.random().toString(36).substring(2, 15);
  }
}
