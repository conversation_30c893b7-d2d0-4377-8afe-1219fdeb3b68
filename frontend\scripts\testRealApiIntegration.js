/**
 * Real API Integration Test Script
 * 
 * This script tests the complete integration between the React Native app
 * and the Django backend APIs, verifying all account operations work correctly.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Test configuration
const TEST_CONFIG = {
  API_URL: process.env.EXPO_PUBLIC_API_URL || 'http://127.0.0.1:8000',
  USE_REAL_API: 'true',
  TIMEOUT: 30000, // 30 seconds
};

// Test results tracking
let testResults = {
  passed: 0,
  failed: 0,
  errors: [],
  details: []
};

/**
 * Log test results
 */
function logResult(testName, passed, details = '') {
  const status = passed ? '✅ PASS' : '❌ FAIL';
  const message = `${status}: ${testName}`;
  
  console.log(message);
  if (details) {
    console.log(`   ${details}`);
  }
  
  testResults.details.push({ testName, passed, details });
  
  if (passed) {
    testResults.passed++;
  } else {
    testResults.failed++;
    testResults.errors.push(`${testName}: ${details}`);
  }
}

/**
 * Check if backend server is running
 */
async function checkBackendServer() {
  console.log('\n🔍 Checking backend server...');
  
  try {
    const response = await fetch(`${TEST_CONFIG.API_URL}/api/health/`, {
      method: 'GET',
      timeout: 5000
    });
    
    if (response.ok) {
      logResult('Backend Server Health Check', true, 'Server is running and responding');
      return true;
    } else {
      logResult('Backend Server Health Check', false, `Server returned status ${response.status}`);
      return false;
    }
  } catch (error) {
    logResult('Backend Server Health Check', false, `Cannot connect to server: ${error.message}`);
    return false;
  }
}

/**
 * Verify environment configuration
 */
function checkEnvironmentConfig() {
  console.log('\n🔧 Checking environment configuration...');
  
  const envPath = path.join(__dirname, '../.env');
  
  try {
    if (!fs.existsSync(envPath)) {
      logResult('Environment File Check', false, '.env file not found');
      return false;
    }
    
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    // Check if USE_REAL_API is set to true
    const useRealApiMatch = envContent.match(/EXPO_PUBLIC_USE_REAL_API=(.+)/);
    if (!useRealApiMatch || useRealApiMatch[1].trim() !== 'true') {
      logResult('Real API Configuration', false, 'EXPO_PUBLIC_USE_REAL_API is not set to true');
      return false;
    }
    
    // Check if API URL is configured
    const apiUrlMatch = envContent.match(/EXPO_PUBLIC_API_URL=(.+)/);
    if (!apiUrlMatch) {
      logResult('API URL Configuration', false, 'EXPO_PUBLIC_API_URL is not configured');
      return false;
    }
    
    logResult('Environment Configuration', true, 'All required environment variables are set');
    return true;
  } catch (error) {
    logResult('Environment Configuration', false, `Error reading .env file: ${error.message}`);
    return false;
  }
}

/**
 * Check TypeScript compilation
 */
function checkTypeScriptCompilation() {
  console.log('\n📝 Checking TypeScript compilation...');
  
  try {
    execSync('npx tsc --noEmit', { 
      cwd: path.join(__dirname, '..'),
      stdio: 'pipe'
    });
    
    logResult('TypeScript Compilation', true, 'No TypeScript errors found');
    return true;
  } catch (error) {
    const errorOutput = error.stdout ? error.stdout.toString() : error.message;
    logResult('TypeScript Compilation', false, `TypeScript errors found: ${errorOutput}`);
    return false;
  }
}

/**
 * Test API service imports and initialization
 */
function testApiServiceImports() {
  console.log('\n📦 Testing API service imports...');
  
  try {
    // Test if all required services can be imported
    const apiServicePath = path.join(__dirname, '../services/apiService.ts');
    const realApiServicePath = path.join(__dirname, '../services/realApiService.ts');
    const enhancedErrorServicePath = path.join(__dirname, '../services/enhancedErrorService.ts');
    
    const requiredFiles = [
      { path: apiServicePath, name: 'API Service' },
      { path: realApiServicePath, name: 'Real API Service' },
      { path: enhancedErrorServicePath, name: 'Enhanced Error Service' }
    ];
    
    for (const file of requiredFiles) {
      if (!fs.existsSync(file.path)) {
        logResult(`${file.name} Import`, false, `File not found: ${file.path}`);
        return false;
      }
    }
    
    logResult('API Service Imports', true, 'All required service files exist');
    return true;
  } catch (error) {
    logResult('API Service Imports', false, `Error checking imports: ${error.message}`);
    return false;
  }
}

/**
 * Test component imports and type definitions
 */
function testComponentImports() {
  console.log('\n🧩 Testing component imports and types...');
  
  try {
    const typesPath = path.join(__dirname, '../types/index.ts');
    const accountsScreenPath = path.join(__dirname, '../app/(tabs)/accounts.tsx');
    const accountDetailsPath = path.join(__dirname, '../app/account-details.tsx');
    const accountCardPath = path.join(__dirname, '../components/AccountCard.tsx');
    
    const requiredFiles = [
      { path: typesPath, name: 'Type Definitions' },
      { path: accountsScreenPath, name: 'Accounts Screen' },
      { path: accountDetailsPath, name: 'Account Details Screen' },
      { path: accountCardPath, name: 'Account Card Component' }
    ];
    
    for (const file of requiredFiles) {
      if (!fs.existsSync(file.path)) {
        logResult(`${file.name} Import`, false, `File not found: ${file.path}`);
        return false;
      }
      
      // Check for mock data imports (should be removed)
      const content = fs.readFileSync(file.path, 'utf8');
      if (content.includes("from '@/data/mockData'") && !file.path.includes('mockData')) {
        logResult(`${file.name} Mock Data Check`, false, 'Still importing mock data');
        return false;
      }
    }
    
    logResult('Component Imports', true, 'All components exist and mock data imports removed');
    return true;
  } catch (error) {
    logResult('Component Imports', false, `Error checking components: ${error.message}`);
    return false;
  }
}

/**
 * Test authentication endpoints
 */
async function testAuthenticationEndpoints() {
  console.log('\n🔐 Testing authentication endpoints...');
  
  try {
    // Test login endpoint
    const loginResponse = await fetch(`${TEST_CONFIG.API_URL}/api/auth/login/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'wrongpassword'
      })
    });
    
    // We expect this to fail with 400/401, which means the endpoint is working
    if (loginResponse.status === 400 || loginResponse.status === 401) {
      logResult('Login Endpoint', true, 'Login endpoint is responding correctly');
    } else {
      logResult('Login Endpoint', false, `Unexpected response status: ${loginResponse.status}`);
      return false;
    }
    
    // Test register endpoint
    const registerResponse = await fetch(`${TEST_CONFIG.API_URL}/api/auth/register/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: 'invalid-email',
        password: 'short'
      })
    });
    
    // We expect this to fail with 400, which means the endpoint is working
    if (registerResponse.status === 400) {
      logResult('Register Endpoint', true, 'Register endpoint is responding correctly');
    } else {
      logResult('Register Endpoint', false, `Unexpected response status: ${registerResponse.status}`);
      return false;
    }
    
    return true;
  } catch (error) {
    logResult('Authentication Endpoints', false, `Error testing auth endpoints: ${error.message}`);
    return false;
  }
}

/**
 * Test account endpoints
 */
async function testAccountEndpoints() {
  console.log('\n🏦 Testing account endpoints...');
  
  try {
    // Test accounts list endpoint (should require authentication)
    const accountsResponse = await fetch(`${TEST_CONFIG.API_URL}/api/accounts/`, {
      method: 'GET',
    });
    
    // We expect this to fail with 401 (unauthorized), which means the endpoint is working
    if (accountsResponse.status === 401) {
      logResult('Accounts List Endpoint', true, 'Accounts endpoint requires authentication correctly');
    } else {
      logResult('Accounts List Endpoint', false, `Unexpected response status: ${accountsResponse.status}`);
      return false;
    }
    
    // Test account creation endpoint (should require authentication)
    const createAccountResponse = await fetch(`${TEST_CONFIG.API_URL}/api/accounts/create/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        account_type: 'savings'
      })
    });
    
    // We expect this to fail with 401 (unauthorized), which means the endpoint is working
    if (createAccountResponse.status === 401) {
      logResult('Account Creation Endpoint', true, 'Account creation endpoint requires authentication correctly');
    } else {
      logResult('Account Creation Endpoint', false, `Unexpected response status: ${createAccountResponse.status}`);
      return false;
    }
    
    return true;
  } catch (error) {
    logResult('Account Endpoints', false, `Error testing account endpoints: ${error.message}`);
    return false;
  }
}

/**
 * Run all integration tests
 */
async function runIntegrationTests() {
  console.log('🚀 Starting Real API Integration Tests...');
  console.log('='.repeat(50));
  
  // Environment and configuration tests
  const envConfigOk = checkEnvironmentConfig();
  const tsCompileOk = checkTypeScriptCompilation();
  const apiImportsOk = testApiServiceImports();
  const componentImportsOk = testComponentImports();
  
  // Backend connectivity tests
  const backendOk = await checkBackendServer();
  
  // API endpoint tests (only if backend is available)
  let authEndpointsOk = false;
  let accountEndpointsOk = false;
  
  if (backendOk) {
    authEndpointsOk = await testAuthenticationEndpoints();
    accountEndpointsOk = await testAccountEndpoints();
  }
  
  // Print final results
  console.log('\n' + '='.repeat(50));
  console.log('📊 Integration Test Results:');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.errors.forEach(error => console.log(`   - ${error}`));
  }
  
  const allTestsPassed = testResults.failed === 0;
  
  if (allTestsPassed) {
    console.log('\n🎉 All integration tests passed! The React Native app is ready for real API integration.');
  } else {
    console.log('\n⚠️  Some tests failed. Please fix the issues before proceeding.');
  }
  
  return allTestsPassed;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runIntegrationTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { runIntegrationTests, TEST_CONFIG };
