"""
Utility functions for account management in ExoBank.

This module provides utility functions for account operations including
account number generation, validation, and other account-related helpers.
"""

import random
import string
from django.db import transaction
from django.core.exceptions import ValidationError
from .models import Account


def generate_account_number():
    """
    Generate a unique 12-digit account number for ExoBank.
    
    The account number format follows banking standards:
    - 12 digits total
    - First 3 digits: Bank identifier (001 for ExoBank)
    - Next 2 digits: Account type identifier
    - Last 7 digits: Unique sequence number
    
    Returns:
        str: A unique 12-digit account number
        
    Raises:
        ValidationError: If unable to generate unique number after max attempts
    """
    bank_code = "001"  # ExoBank identifier
    max_attempts = 100
    
    for attempt in range(max_attempts):
        # Generate account type code based on common patterns
        account_type_code = random.choice(['10', '20', '30'])  # 10=Savings, 20=Checking, 30=Business
        
        # Generate 7-digit unique sequence
        sequence = ''.join([str(random.randint(0, 9)) for _ in range(7)])
        
        # Combine to form 12-digit account number
        account_number = f"{bank_code}{account_type_code}{sequence}"
        
        # Check if this account number already exists
        if not Account.objects.filter(account_number=account_number).exists():
            return account_number
    
    # If we couldn't generate a unique number after max attempts
    raise ValidationError(
        f"Unable to generate unique account number after {max_attempts} attempts. "
        "Please try again or contact system administrator."
    )


def generate_account_number_for_type(account_type):
    """
    Generate a unique account number for a specific account type.
    
    Args:
        account_type (str): The account type ('savings', 'checking', 'business')
        
    Returns:
        str: A unique 12-digit account number with appropriate type code
        
    Raises:
        ValidationError: If invalid account type or unable to generate unique number
    """
    bank_code = "001"  # ExoBank identifier
    
    # Map account types to their codes
    type_codes = {
        'savings': '10',
        'checking': '20', 
        'business': '30'
    }
    
    if account_type not in type_codes:
        raise ValidationError(f"Invalid account type: {account_type}")
    
    account_type_code = type_codes[account_type]
    max_attempts = 100
    
    for attempt in range(max_attempts):
        # Generate 7-digit unique sequence
        sequence = ''.join([str(random.randint(0, 9)) for _ in range(7)])
        
        # Combine to form 12-digit account number
        account_number = f"{bank_code}{account_type_code}{sequence}"
        
        # Check if this account number already exists
        if not Account.objects.filter(account_number=account_number).exists():
            return account_number
    
    # If we couldn't generate a unique number after max attempts
    raise ValidationError(
        f"Unable to generate unique account number for type {account_type} "
        f"after {max_attempts} attempts. Please try again."
    )


def validate_account_number(account_number):
    """
    Validate an account number format and uniqueness.
    
    Args:
        account_number (str): The account number to validate
        
    Returns:
        bool: True if valid
        
    Raises:
        ValidationError: If account number is invalid
    """
    if not account_number:
        raise ValidationError("Account number is required.")
    
    # Remove any spaces or dashes
    account_number = account_number.replace(' ', '').replace('-', '')
    
    # Check length
    if len(account_number) != 12:
        raise ValidationError("Account number must be exactly 12 digits.")
    
    # Check if all characters are digits
    if not account_number.isdigit():
        raise ValidationError("Account number must contain only digits.")
    
    # Check bank code (first 3 digits should be 001 for ExoBank)
    if not account_number.startswith('001'):
        raise ValidationError("Invalid bank code. Account number must start with 001.")
    
    # Check account type code (4th and 5th digits)
    type_code = account_number[3:5]
    valid_type_codes = ['10', '20', '30']
    if type_code not in valid_type_codes:
        raise ValidationError(f"Invalid account type code: {type_code}")
    
    return True


def check_account_number_uniqueness(account_number, exclude_account_id=None):
    """
    Check if an account number is unique in the database.
    
    Args:
        account_number (str): The account number to check
        exclude_account_id (UUID, optional): Account ID to exclude from check (for updates)
        
    Returns:
        bool: True if unique, False if already exists
    """
    queryset = Account.objects.filter(account_number=account_number)
    
    if exclude_account_id:
        queryset = queryset.exclude(id=exclude_account_id)
    
    return not queryset.exists()


def get_account_type_from_number(account_number):
    """
    Extract account type from account number.
    
    Args:
        account_number (str): The 12-digit account number
        
    Returns:
        str: Account type ('savings', 'checking', 'business') or None if invalid
    """
    if not account_number or len(account_number) != 12:
        return None
    
    type_code = account_number[3:5]
    type_mapping = {
        '10': 'savings',
        '20': 'checking',
        '30': 'business'
    }
    
    return type_mapping.get(type_code)


@transaction.atomic
def create_account_with_number(user, account_type, **kwargs):
    """
    Create a new account with auto-generated account number.
    
    This function ensures atomic creation of an account with a unique
    account number, preventing race conditions.
    
    Args:
        user: User instance who owns the account
        account_type (str): Type of account to create
        **kwargs: Additional account fields
        
    Returns:
        Account: The created account instance
        
    Raises:
        ValidationError: If account creation fails
    """
    # Generate unique account number for the specified type
    account_number = generate_account_number_for_type(account_type)
    
    # Create the account
    account_data = {
        'user': user,
        'account_number': account_number,
        'account_type': account_type,
        **kwargs
    }
    
    account = Account.objects.create(**account_data)
    return account


def format_account_number_display(account_number):
    """
    Format account number for display purposes.
    
    Args:
        account_number (str): The 12-digit account number
        
    Returns:
        str: Formatted account number (e.g., "001-20-1234567")
    """
    if not account_number or len(account_number) != 12:
        return account_number
    
    return f"{account_number[:3]}-{account_number[3:5]}-{account_number[5:]}"


def mask_account_number(account_number, show_last=4):
    """
    Mask account number for security purposes.
    
    Args:
        account_number (str): The account number to mask
        show_last (int): Number of digits to show at the end
        
    Returns:
        str: Masked account number (e.g., "****-**-**4567")
    """
    if not account_number or len(account_number) < show_last:
        return account_number
    
    masked_length = len(account_number) - show_last
    masked_part = '*' * masked_length
    visible_part = account_number[-show_last:]
    
    # Format with dashes if it's a 12-digit ExoBank account number
    if len(account_number) == 12:
        return f"***-**-**{visible_part}"
    
    return f"{masked_part}{visible_part}"
