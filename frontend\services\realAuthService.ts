/**
 * Real Authentication Service for ExoBank Frontend
 *
 * This service provides real JWT authentication functionality
 * to replace mock authentication during backend integration.
 */

import realApiService, { AuthUser, ApiResponse } from './realApiService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  validateJWTToken,
  isTokenExpired,
  shouldRefreshToken,
  getTokenExpirationTime,
  JWT_CONFIG,
  JWTError
} from '../utils/jwtUtils';
import {
  AuthSession,
  AuthResponse,
  AuthEvent,
  AuthStateChangeCallback,
  AuthSubscription,
  AuthError,
  createAuthError,
  AuthErrorCode,
  AUTH_CONFIG
} from '../utils/authTypes';

// Re-export types for backward compatibility
export type { AuthResponse, AuthSession, AuthStateChangeCallback };

class RealAuthService {
  private authStateListeners: AuthStateChangeCallback[] = [];
  private currentSession: AuthSession | null = null;
  private refreshPromise: Promise<boolean> | null = null;

  // Sign in with email and password
  async signIn(email: string, password: string): Promise<AuthResponse> {
    try {
      const response = await realApiService.login({ email, password });

      if (!response.success) {
        const errorCode = this.mapApiErrorToAuthError(response.error);
        return {
          user: null,
          error: createAuthError(errorCode, response.error || 'Login failed'),
        };
      }

      const loginData = response.data;
      if (loginData && loginData.user && loginData.access) {
        // Validate the received JWT token
        const tokenValidation = validateJWTToken(loginData.access);
        if (!tokenValidation.isValid) {
          return {
            user: null,
            error: createAuthError(AuthErrorCode.TOKEN_INVALID, 'Received invalid token from server'),
          };
        }

        // Create session with expiration time
        const expiresAt = getTokenExpirationTime(loginData.access);
        this.currentSession = {
          user: loginData.user,
          access_token: loginData.access,
          refresh_token: loginData.refresh,
          expires_at: expiresAt || undefined,
          created_at: Date.now(),
        };

        // Store session data
        await this.storeSessionData(this.currentSession);

        // Notify listeners
        this.notifyAuthStateChange(AuthEvent.SIGNED_IN, this.currentSession);

        return {
          user: loginData.user,
          error: null,
        };
      } else {
        return {
          user: null,
          error: createAuthError(AuthErrorCode.SERVER_ERROR, 'Invalid response from server'),
        };
      }
    } catch (error: any) {
      return {
        user: null,
        error: createAuthError(AuthErrorCode.NETWORK_ERROR, error.message || 'Network error occurred'),
      };
    }
  }

  // Sign up with email and password
  async signUp(email: string, password: string, metadata?: any): Promise<AuthResponse> {
    try {
      const registerData = {
        username: metadata?.username || email.split('@')[0],
        email,
        password,
        password_confirm: password,
        first_name: metadata?.first_name || '',
        last_name: metadata?.last_name || '',
        phone_number: metadata?.phone || '',
      };

      const response = await realApiService.register(registerData);

      if (!response.success) {
        return {
          user: null,
          error: { message: response.error || 'Registration failed' },
        };
      }

      return {
        user: response.data?.user || null,
        error: null,
      };
    } catch (error: any) {
      return {
        user: null,
        error: { message: error.message || 'Network error occurred' },
      };
    }
  }

  // Sign out
  async signOut(): Promise<{ error: AuthError | null }> {
    try {
      // Call backend logout endpoint to blacklist tokens
      await realApiService.logout();

      // Clear local session
      await this.clearSession();

      // Notify listeners
      this.notifyAuthStateChange(AuthEvent.SIGNED_OUT, null);

      return { error: null };
    } catch (error: any) {
      // Even if backend call fails, clear local session
      await this.clearSession();
      this.notifyAuthStateChange(AuthEvent.SIGNED_OUT, null);

      return {
        error: createAuthError(
          AuthErrorCode.NETWORK_ERROR,
          error.message || 'Logout failed, but local session cleared'
        )
      };
    }
  }

  // Clear session data from memory and storage
  private async clearSession(): Promise<void> {
    try {
      this.currentSession = null;

      // Clear all auth-related data from storage
      await AsyncStorage.multiRemove([
        JWT_CONFIG.ACCESS_TOKEN_KEY,
        JWT_CONFIG.REFRESH_TOKEN_KEY,
        JWT_CONFIG.USER_DATA_KEY,
        'session_metadata'
      ]);

      // Also clear from realApiService for backward compatibility
      await realApiService.clearAuthData();
    } catch (error) {
      console.error('Error clearing session:', error);
    }
  }

  // Get current user
  async getUser(): Promise<AuthUser | null> {
    try {
      // First check current session
      if (this.currentSession) {
        return this.currentSession.user;
      }

      // Try to get from API service
      const response = await realApiService.getCurrentUser();
      if (response.success && response.data) {
        return response.data;
      }

      return null;
    } catch (error) {
      console.error('Error getting user:', error);
      return null;
    }
  }

  // Get current session
  async getSession(): Promise<{ data: { session: AuthSession | null } }> {
    try {
      // Use current session if available and valid
      if (this.currentSession) {
        const validation = validateJWTToken(this.currentSession.access_token);
        if (validation.isValid) {
          return {
            data: { session: this.currentSession }
          };
        }
      }

      // Load session from storage
      const session = await this.loadSessionData();

      if (session) {
        // Validate the loaded session
        const validation = validateJWTToken(session.access_token);

        if (validation.isValid) {
          this.currentSession = session;
          return {
            data: { session: this.currentSession }
          };
        } else if (session.refresh_token) {
          // Try to refresh the token
          this.currentSession = session;
          const refreshSuccess = await this.performTokenRefresh();

          if (refreshSuccess) {
            return {
              data: { session: this.currentSession }
            };
          }
        }
      }

      // No valid session found
      this.currentSession = null;
      return {
        data: { session: null }
      };
    } catch (error) {
      console.error('Error getting session:', error);
      this.currentSession = null;
      return {
        data: { session: null }
      };
    }
  }

  // Update user profile
  async updateUser(updates: Partial<AuthUser>): Promise<AuthResponse> {
    try {
      const currentUser = await this.getUser();
      if (!currentUser) {
        return {
          user: null,
          error: { message: 'No authenticated user' },
        };
      }

      // For now, just update locally since we don't have a user update endpoint
      const updatedUser = { ...currentUser, ...updates };
      await realApiService.saveUser(updatedUser);

      // Update current session
      if (this.currentSession) {
        this.currentSession.user = updatedUser;
      }

      return {
        user: updatedUser,
        error: null,
      };
    } catch (error: any) {
      return {
        user: null,
        error: { message: error.message || 'Update failed' },
      };
    }
  }

  // Reset password
  async resetPasswordForEmail(email: string): Promise<{ data: any; error: any }> {
    try {
      // TODO: Implement password reset endpoint when available
      // For now, simulate the request
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return {
        data: { message: 'Password reset email sent' },
        error: null,
      };
    } catch (error: any) {
      return {
        data: null,
        error: { message: error.message || 'Password reset failed' },
      };
    }
  }

  // Update password
  async updatePassword(password: string): Promise<{ error: any }> {
    try {
      // TODO: Implement password update endpoint when available
      // For now, simulate the request
      await new Promise(resolve => setTimeout(resolve, 500));
      
      return { error: null };
    } catch (error: any) {
      return { error: { message: error.message || 'Password update failed' } };
    }
  }

  // Listen to auth state changes
  onAuthStateChange(callback: AuthStateChangeCallback) {
    this.authStateListeners.push(callback);

    // Return subscription object
    return {
      data: {
        subscription: {
          unsubscribe: () => {
            const index = this.authStateListeners.indexOf(callback);
            if (index > -1) {
              this.authStateListeners.splice(index, 1);
            }
          }
        }
      }
    };
  }

  // Notify auth state change listeners
  private notifyAuthStateChange(event: AuthEvent, session: AuthSession | null) {
    this.authStateListeners.forEach(callback => {
      try {
        callback(event, session);
      } catch (error) {
        console.error('Error in auth state change callback:', error);
      }
    });
  }

  // Map API errors to auth error codes
  private mapApiErrorToAuthError(error: string | undefined): AuthErrorCode {
    if (!error) return AuthErrorCode.UNKNOWN_ERROR;

    const errorLower = error.toLowerCase();

    if (errorLower.includes('invalid') && errorLower.includes('credentials')) {
      return AuthErrorCode.INVALID_CREDENTIALS;
    }
    if (errorLower.includes('inactive')) {
      return AuthErrorCode.ACCOUNT_INACTIVE;
    }
    if (errorLower.includes('suspended')) {
      return AuthErrorCode.ACCOUNT_SUSPENDED;
    }
    if (errorLower.includes('pending')) {
      return AuthErrorCode.ACCOUNT_PENDING;
    }
    if (errorLower.includes('network')) {
      return AuthErrorCode.NETWORK_ERROR;
    }

    return AuthErrorCode.UNKNOWN_ERROR;
  }

  // Store session data securely
  private async storeSessionData(session: AuthSession): Promise<void> {
    try {
      await AsyncStorage.setItem(JWT_CONFIG.ACCESS_TOKEN_KEY, session.access_token);
      await AsyncStorage.setItem(JWT_CONFIG.REFRESH_TOKEN_KEY, session.refresh_token);
      await AsyncStorage.setItem(JWT_CONFIG.USER_DATA_KEY, JSON.stringify(session.user));

      // Store session metadata
      const sessionMetadata = {
        expires_at: session.expires_at,
        created_at: session.created_at,
      };
      await AsyncStorage.setItem('session_metadata', JSON.stringify(sessionMetadata));
    } catch (error) {
      console.error('Error storing session data:', error);
    }
  }

  // Load session data from storage
  private async loadSessionData(): Promise<AuthSession | null> {
    try {
      const accessToken = await AsyncStorage.getItem(JWT_CONFIG.ACCESS_TOKEN_KEY);
      const refreshToken = await AsyncStorage.getItem(JWT_CONFIG.REFRESH_TOKEN_KEY);
      const userData = await AsyncStorage.getItem(JWT_CONFIG.USER_DATA_KEY);
      const sessionMetadata = await AsyncStorage.getItem('session_metadata');

      if (!accessToken || !userData) {
        return null;
      }

      const user = JSON.parse(userData);
      const metadata = sessionMetadata ? JSON.parse(sessionMetadata) : {};

      return {
        user,
        access_token: accessToken,
        refresh_token: refreshToken || '',
        expires_at: metadata.expires_at,
        created_at: metadata.created_at || Date.now(),
      };
    } catch (error) {
      console.error('Error loading session data:', error);
      return null;
    }
  }

  // Get access token for API calls with automatic refresh
  async getAccessToken(): Promise<string | null> {
    try {
      // Ensure we have a current session
      if (!this.currentSession) {
        this.currentSession = await this.loadSessionData();
      }

      if (!this.currentSession) {
        return null;
      }

      const token = this.currentSession.access_token;

      // Validate token and check if refresh is needed
      const validation = validateJWTToken(token, AUTH_CONFIG.TOKEN_REFRESH_THRESHOLD_MINUTES);

      if (!validation.isValid) {
        // Token is invalid or expired, try to refresh
        const refreshSuccess = await this.performTokenRefresh();
        if (refreshSuccess && this.currentSession) {
          return this.currentSession.access_token;
        }
        return null;
      }

      if (validation.shouldRefresh) {
        // Token is valid but should be refreshed soon
        // Perform refresh in background (don't wait for it)
        this.performTokenRefresh().catch(error => {
          console.warn('Background token refresh failed:', error);
        });
      }

      return token;
    } catch (error) {
      console.error('Error getting access token:', error);
      return null;
    }
  }

  // Refresh session
  async refreshSession(): Promise<{ data: { session: AuthSession | null }; error: AuthError | null }> {
    const refreshSuccess = await this.performTokenRefresh();

    if (refreshSuccess) {
      return {
        data: { session: this.currentSession },
        error: null,
      };
    } else {
      return {
        data: { session: null },
        error: createAuthError(AuthErrorCode.TOKEN_REFRESH_FAILED, 'Session refresh failed'),
      };
    }
  }

  // Perform token refresh with proper error handling and concurrency control
  private async performTokenRefresh(): Promise<boolean> {
    // Prevent multiple concurrent refresh attempts
    if (this.refreshPromise) {
      return await this.refreshPromise;
    }

    this.refreshPromise = this._performTokenRefresh();

    try {
      const result = await this.refreshPromise;
      return result;
    } finally {
      this.refreshPromise = null;
    }
  }

  private async _performTokenRefresh(): Promise<boolean> {
    try {
      // Ensure we have a current session with refresh token
      if (!this.currentSession) {
        this.currentSession = await this.loadSessionData();
      }

      if (!this.currentSession || !this.currentSession.refresh_token) {
        console.warn('No refresh token available for token refresh');
        return false;
      }

      const response = await realApiService.refreshToken();

      if (response.success && response.data) {
        // Validate the new access token
        const tokenValidation = validateJWTToken(response.data.access);
        if (!tokenValidation.isValid) {
          console.error('Received invalid token from refresh endpoint');
          return false;
        }

        // Update current session with new access token
        const expiresAt = getTokenExpirationTime(response.data.access);
        this.currentSession.access_token = response.data.access;
        this.currentSession.expires_at = expiresAt || undefined;

        // Store updated session
        await this.storeSessionData(this.currentSession);

        // Notify listeners
        this.notifyAuthStateChange(AuthEvent.TOKEN_REFRESHED, this.currentSession);

        return true;
      } else {
        console.error('Token refresh failed:', response.error);

        // If refresh fails, clear session and notify listeners
        await this.clearSession();
        this.notifyAuthStateChange(AuthEvent.SESSION_EXPIRED, null);

        return false;
      }
    } catch (error: any) {
      console.error('Error during token refresh:', error);

      // On error, clear session and notify listeners
      await this.clearSession();
      this.notifyAuthStateChange(AuthEvent.SESSION_EXPIRED, null);

      return false;
    }
  }

  // Set session
  async setSession(session: AuthSession | null): Promise<{ error: any }> {
    try {
      this.currentSession = session;
      
      if (session) {
        await realApiService.saveUser(session.user);
        await realApiService.saveToken(session.access_token);
        await AsyncStorage.setItem('refresh_token', session.refresh_token);
      } else {
        await realApiService.clearAuthData();
      }
      
      return { error: null };
    } catch (error: any) {
      return { error: { message: error.message || 'Set session failed' } };
    }
  }

  // Exchange code for session (OAuth flows)
  async exchangeCodeForSession(code: string): Promise<{ data: { session: AuthSession | null }; error: any }> {
    // OAuth not implemented in backend yet
    return {
      data: { session: null },
      error: { message: 'OAuth not implemented' },
    };
  }

  // Get user by ID
  async getUserById(id: string): Promise<AuthUser | null> {
    try {
      const currentUser = await this.getUser();
      if (currentUser && currentUser.id === id) {
        return currentUser;
      }
      return null;
    } catch (error) {
      console.error('Error getting user by ID:', error);
      return null;
    }
  }

  // Check if user is authenticated
  async isAuthenticated(): Promise<boolean> {
    try {
      // Get current session
      const sessionData = await this.getSession();
      const session = sessionData.data.session;

      if (!session) {
        return false;
      }

      // Validate the access token
      const validation = validateJWTToken(session.access_token);
      return validation.isValid;
    } catch (error) {
      console.error('Error checking authentication:', error);
      return false;
    }
  }

  // Initialize session from stored data
  async initializeSession(): Promise<void> {
    try {
      const session = await this.loadSessionData();

      if (session) {
        // Validate the stored token
        const validation = validateJWTToken(session.access_token);

        if (validation.isValid) {
          this.currentSession = session;
          this.notifyAuthStateChange(AuthEvent.SIGNED_IN, this.currentSession);

          // Check if token needs refresh
          if (validation.shouldRefresh) {
            this.performTokenRefresh().catch(error => {
              console.warn('Initial token refresh failed:', error);
            });
          }
        } else {
          // Token is invalid, try to refresh if we have a refresh token
          if (session.refresh_token) {
            this.currentSession = session;
            const refreshSuccess = await this.performTokenRefresh();

            if (!refreshSuccess) {
              // Refresh failed, clear invalid session
              await this.clearSession();
            }
          } else {
            // No refresh token, clear invalid session
            await this.clearSession();
          }
        }
      }
    } catch (error) {
      console.error('Error initializing session:', error);
      await this.clearSession();
    }
  }
}

// Export singleton instance
const realAuthService = new RealAuthService();
export default realAuthService;