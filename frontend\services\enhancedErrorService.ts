/**
 * Enhanced Error Service for ExoBank Frontend
 * 
 * Provides comprehensive error handling for API calls, network issues,
 * authentication failures, and user-friendly error messages.
 */

import { Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';

export enum ErrorType {
  NETWORK = 'NETWORK',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  VALIDATION = 'VALIDATION',
  SERVER = 'SERVER',
  UNKNOWN = 'UNKNOWN',
  OFFLINE = 'OFFLINE',
  TIMEOUT = 'TIMEOUT',
}

export interface ErrorDetails {
  type: ErrorType;
  message: string;
  userMessage: string;
  code?: string;
  statusCode?: number;
  retryable: boolean;
  requiresAuth: boolean;
  timestamp: Date;
  context?: any;
}

export interface ErrorHandlerOptions {
  showAlert?: boolean;
  logError?: boolean;
  retryCallback?: () => Promise<void>;
  fallbackMessage?: string;
  context?: any;
}

class EnhancedErrorService {
  private errorLog: ErrorDetails[] = [];
  private maxLogSize = 100;

  /**
   * Parse and categorize errors from API responses
   */
  parseError(error: any, context?: any): ErrorDetails {
    const timestamp = new Date();
    let errorDetails: ErrorDetails = {
      type: ErrorType.UNKNOWN,
      message: 'An unknown error occurred',
      userMessage: 'Something went wrong. Please try again.',
      retryable: true,
      requiresAuth: false,
      timestamp,
      context,
    };

    // Handle network errors
    if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('Network')) {
      errorDetails = {
        ...errorDetails,
        type: ErrorType.NETWORK,
        message: 'Network connection failed',
        userMessage: 'Please check your internet connection and try again.',
        retryable: true,
      };
    }
    // Handle authentication errors
    else if (error?.status === 401 || error?.statusCode === 401) {
      errorDetails = {
        ...errorDetails,
        type: ErrorType.AUTHENTICATION,
        message: 'Authentication failed',
        userMessage: 'Your session has expired. Please log in again.',
        retryable: false,
        requiresAuth: true,
        statusCode: 401,
      };
    }
    // Handle authorization errors
    else if (error?.status === 403 || error?.statusCode === 403) {
      errorDetails = {
        ...errorDetails,
        type: ErrorType.AUTHORIZATION,
        message: 'Access denied',
        userMessage: 'You do not have permission to perform this action.',
        retryable: false,
        statusCode: 403,
      };
    }
    // Handle validation errors
    else if (error?.status === 400 || error?.statusCode === 400) {
      errorDetails = {
        ...errorDetails,
        type: ErrorType.VALIDATION,
        message: error?.message || 'Validation failed',
        userMessage: error?.message || 'Please check your input and try again.',
        retryable: true,
        statusCode: 400,
      };
    }
    // Handle server errors
    else if (error?.status >= 500 || error?.statusCode >= 500) {
      errorDetails = {
        ...errorDetails,
        type: ErrorType.SERVER,
        message: 'Server error',
        userMessage: 'Our servers are experiencing issues. Please try again later.',
        retryable: true,
        statusCode: error?.status || error?.statusCode,
      };
    }
    // Handle timeout errors
    else if (error?.code === 'TIMEOUT' || error?.message?.includes('timeout')) {
      errorDetails = {
        ...errorDetails,
        type: ErrorType.TIMEOUT,
        message: 'Request timeout',
        userMessage: 'The request took too long. Please try again.',
        retryable: true,
      };
    }
    // Handle offline errors
    else if (error?.code === 'OFFLINE' || error?.message?.includes('offline')) {
      errorDetails = {
        ...errorDetails,
        type: ErrorType.OFFLINE,
        message: 'Device is offline',
        userMessage: 'You are currently offline. Some features may not be available.',
        retryable: true,
      };
    }
    // Handle specific error messages
    else if (error?.message) {
      errorDetails = {
        ...errorDetails,
        message: error.message,
        userMessage: this.getUserFriendlyMessage(error.message),
      };
    }

    return errorDetails;
  }

  /**
   * Convert technical error messages to user-friendly messages
   */
  private getUserFriendlyMessage(message: string): string {
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('account not found')) {
      return 'Account not found. Please check the account details.';
    }
    if (lowerMessage.includes('insufficient funds')) {
      return 'Insufficient funds in your account.';
    }
    if (lowerMessage.includes('invalid credentials')) {
      return 'Invalid email or password. Please try again.';
    }
    if (lowerMessage.includes('email already exists')) {
      return 'An account with this email already exists.';
    }
    if (lowerMessage.includes('password too weak')) {
      return 'Password is too weak. Please choose a stronger password.';
    }
    if (lowerMessage.includes('rate limit')) {
      return 'Too many requests. Please wait a moment and try again.';
    }

    return 'Something went wrong. Please try again.';
  }

  /**
   * Handle errors with comprehensive error management
   */
  async handleError(error: any, options: ErrorHandlerOptions = {}): Promise<void> {
    const {
      showAlert = true,
      logError = true,
      retryCallback,
      fallbackMessage,
      context,
    } = options;

    const errorDetails = this.parseError(error, context);

    // Log error if enabled
    if (logError) {
      this.logError(errorDetails);
    }

    // Handle authentication errors
    if (errorDetails.requiresAuth) {
      await this.handleAuthenticationError();
      return;
    }

    // Show user-friendly alert if enabled
    if (showAlert) {
      this.showErrorAlert(errorDetails, retryCallback, fallbackMessage);
    }
  }

  /**
   * Handle authentication errors by clearing auth data and redirecting to login
   */
  private async handleAuthenticationError(): Promise<void> {
    try {
      // Clear authentication data
      await AsyncStorage.multiRemove(['auth_token', 'refresh_token', 'user_data']);
      
      // Show alert and redirect to login
      Alert.alert(
        'Session Expired',
        'Your session has expired. Please log in again.',
        [
          {
            text: 'OK',
            onPress: () => router.replace('/login'),
          },
        ]
      );
    } catch (clearError) {
      console.error('Error clearing auth data:', clearError);
    }
  }

  /**
   * Show user-friendly error alert
   */
  private showErrorAlert(
    errorDetails: ErrorDetails,
    retryCallback?: () => Promise<void>,
    fallbackMessage?: string
  ): void {
    const message = fallbackMessage || errorDetails.userMessage;
    const buttons: any[] = [];

    // Add retry button if error is retryable and callback is provided
    if (errorDetails.retryable && retryCallback) {
      buttons.push({
        text: 'Retry',
        onPress: retryCallback,
      });
    }

    // Always add OK button
    buttons.push({
      text: 'OK',
      style: 'cancel',
    });

    Alert.alert('Error', message, buttons);
  }

  /**
   * Log error details for debugging
   */
  private logError(errorDetails: ErrorDetails): void {
    console.error('Error logged:', errorDetails);

    // Add to error log
    this.errorLog.unshift(errorDetails);

    // Maintain log size
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize);
    }
  }

  /**
   * Get recent error logs for debugging
   */
  getErrorLogs(): ErrorDetails[] {
    return [...this.errorLog];
  }

  /**
   * Clear error logs
   */
  clearErrorLogs(): void {
    this.errorLog = [];
  }

  /**
   * Check if error is retryable
   */
  isRetryable(error: any): boolean {
    const errorDetails = this.parseError(error);
    return errorDetails.retryable;
  }

  /**
   * Get error type for conditional handling
   */
  getErrorType(error: any): ErrorType {
    const errorDetails = this.parseError(error);
    return errorDetails.type;
  }
}

// Export singleton instance
const enhancedErrorService = new EnhancedErrorService();
export default enhancedErrorService;
