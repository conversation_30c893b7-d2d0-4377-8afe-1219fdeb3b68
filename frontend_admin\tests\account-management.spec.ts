/**
 * Account Management Component Tests
 * 
 * Tests for the account management dialogs and API integration
 */

import { test, expect } from '@playwright/test'

// Mock account data for testing
const mockAccount = {
  id: 'test-account-id',
  account_number: '**********',
  account_type: 'savings',
  balance: 1000.00,
  status: 'active',
  user_email: '<EMAIL>',
  user_name: 'Test User'
}

test.describe('Account Management Integration', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to admin dashboard (assuming authentication is handled)
    await page.goto('/dashboard')
    
    // Wait for page to load
    await page.waitForLoadState('networkidle')
  })

  test.describe('Edit Account Dialog', () => {
    test('should display edit account dialog with correct fields', async ({ page }) => {
      // Mock the account data in localStorage or through API
      await page.evaluate((account) => {
        window.localStorage.setItem('test-account', JSON.stringify(account))
      }, mockAccount)

      // Look for edit button or trigger (this depends on your UI structure)
      // You may need to adjust this selector based on your actual implementation
      const editButton = page.locator('[data-testid="edit-account-button"]').first()
      
      if (await editButton.count() > 0) {
        await editButton.click()
        
        // Check if dialog opens
        await expect(page.locator('[role="dialog"]')).toBeVisible()
        await expect(page.locator('text=Edit Account')).toBeVisible()
        
        // Check form fields are present
        await expect(page.locator('label:has-text("Account Type")')).toBeVisible()
        await expect(page.locator('label:has-text("Balance")')).toBeVisible()
        await expect(page.locator('label:has-text("Status")')).toBeVisible()
        
        // Check account type options
        await page.locator('button:has-text("Select")').first().click()
        await expect(page.locator('text=Checking')).toBeVisible()
        await expect(page.locator('text=Savings')).toBeVisible()
        await expect(page.locator('text=Business')).toBeVisible()
      }
    })

    test('should show role-based restrictions for non-admin users', async ({ page }) => {
      // Mock a staff user (limited permissions)
      await page.evaluate(() => {
        const staffUser = {
          id: 'staff-user-id',
          email: '<EMAIL>',
          role: 'staff',
          first_name: 'Staff',
          last_name: 'User'
        }
        window.localStorage.setItem('exobank_admin_auth', JSON.stringify({
          access: 'mock-token',
          refresh: 'mock-refresh',
          user: staffUser
        }))
      })

      // Reload page to apply new user context
      await page.reload()
      await page.waitForLoadState('networkidle')

      // Try to open edit dialog
      const editButton = page.locator('[data-testid="edit-account-button"]').first()
      
      if (await editButton.count() > 0) {
        await editButton.click()
        
        // Check if dialog opens
        await expect(page.locator('[role="dialog"]')).toBeVisible()
        
        // Check that certain fields are disabled for staff users
        const accountTypeSelect = page.locator('select[name="account_type"], button:has-text("Select")').first()
        const balanceInput = page.locator('input[type="number"]')
        
        // Account type should be disabled for non-admin users
        if (await accountTypeSelect.count() > 0) {
          await expect(accountTypeSelect).toBeDisabled()
        }
        
        // Balance should be disabled for non-manager users
        if (await balanceInput.count() > 0) {
          await expect(balanceInput).toBeDisabled()
        }
        
        // Check for permission messages
        await expect(page.locator('text=Only admins can change account type')).toBeVisible()
        await expect(page.locator('text=Only managers and above can adjust balance')).toBeVisible()
      }
    })
  })

  test.describe('Create Account Dialog', () => {
    test('should display create account dialog with all required fields', async ({ page }) => {
      // Look for create account button
      const createButton = page.locator('button:has-text("Create Account"), button:has-text("New Account")').first()
      
      if (await createButton.count() > 0) {
        await createButton.click()
        
        // Check if dialog opens
        await expect(page.locator('[role="dialog"]')).toBeVisible()
        await expect(page.locator('text=Create New Account')).toBeVisible()
        
        // Check customer information fields
        await expect(page.locator('label:has-text("Full Name")')).toBeVisible()
        await expect(page.locator('label:has-text("Email Address")')).toBeVisible()
        await expect(page.locator('label:has-text("Phone Number")')).toBeVisible()
        await expect(page.locator('label:has-text("Address")')).toBeVisible()
        
        // Check account details fields
        await expect(page.locator('label:has-text("Account Type")')).toBeVisible()
        await expect(page.locator('label:has-text("Initial Deposit")')).toBeVisible()
        await expect(page.locator('label:has-text("Notes")')).toBeVisible()
        
        // Check form validation
        const submitButton = page.locator('button:has-text("Create Account")')
        await submitButton.click()
        
        // Should show validation errors for required fields
        // (This depends on your validation implementation)
      }
    })

    test('should restrict business account creation for non-manager users', async ({ page }) => {
      // Mock a staff user
      await page.evaluate(() => {
        const staffUser = {
          id: 'staff-user-id',
          email: '<EMAIL>',
          role: 'staff',
          first_name: 'Staff',
          last_name: 'User'
        }
        window.localStorage.setItem('exobank_admin_auth', JSON.stringify({
          access: 'mock-token',
          refresh: 'mock-refresh',
          user: staffUser
        }))
      })

      await page.reload()
      await page.waitForLoadState('networkidle')

      // Try to open create dialog
      const createButton = page.locator('button:has-text("Create Account"), button:has-text("New Account")').first()
      
      if (await createButton.count() > 0) {
        await createButton.click()
        
        // Check if dialog opens
        await expect(page.locator('[role="dialog"]')).toBeVisible()
        
        // Open account type dropdown
        await page.locator('button:has-text("Select account type")').click()
        
        // Business account option should not be visible for staff
        await expect(page.locator('text=Business Account')).not.toBeVisible()
        
        // Should show permission message
        await expect(page.locator('text=Business accounts require manager approval')).toBeVisible()
      }
    })

    test('should show access denied for customers', async ({ page }) => {
      // Mock a customer user
      await page.evaluate(() => {
        const customerUser = {
          id: 'customer-user-id',
          email: '<EMAIL>',
          role: 'customer',
          first_name: 'Customer',
          last_name: 'User'
        }
        window.localStorage.setItem('exobank_admin_auth', JSON.stringify({
          access: 'mock-token',
          refresh: 'mock-refresh',
          user: customerUser
        }))
      })

      await page.reload()
      await page.waitForLoadState('networkidle')

      // Try to open create dialog
      const createButton = page.locator('button:has-text("Create Account"), button:has-text("New Account")').first()
      
      if (await createButton.count() > 0) {
        await createButton.click()
        
        // Should show access denied dialog
        await expect(page.locator('text=Access Denied')).toBeVisible()
        await expect(page.locator('text=You don\'t have permission to create accounts')).toBeVisible()
      }
    })
  })

  test.describe('API Integration', () => {
    test('should handle API errors gracefully', async ({ page }) => {
      // Mock API failure
      await page.route('**/api/v1/accounts/**', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({
            success: false,
            error: {
              message: 'Internal server error',
              code: 'SERVER_ERROR'
            }
          })
        })
      })

      // Try to perform an action that would trigger API call
      const editButton = page.locator('[data-testid="edit-account-button"]').first()
      
      if (await editButton.count() > 0) {
        await editButton.click()
        
        // Fill form and submit
        await page.fill('input[type="number"]', '500')
        await page.click('button:has-text("Update Account")')
        
        // Should show error message
        await expect(page.locator('text=Failed to update account')).toBeVisible()
      }
    })

    test('should show loading states during API calls', async ({ page }) => {
      // Mock slow API response
      await page.route('**/api/v1/accounts/**', route => {
        setTimeout(() => {
          route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify({
              success: true,
              data: mockAccount
            })
          })
        }, 2000)
      })

      // Try to perform an action
      const editButton = page.locator('[data-testid="edit-account-button"]').first()
      
      if (await editButton.count() > 0) {
        await editButton.click()
        
        // Fill form and submit
        await page.fill('input[type="number"]', '500')
        await page.click('button:has-text("Update Account")')
        
        // Should show loading spinner
        await expect(page.locator('.animate-spin')).toBeVisible()
        
        // Wait for completion
        await expect(page.locator('.animate-spin')).not.toBeVisible({ timeout: 5000 })
      }
    })
  })
})
