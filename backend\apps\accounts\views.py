"""
Views for account management.
"""

from rest_framework import generics, filters, status
from rest_framework.response import Response
from rest_framework.decorators import action
from django_filters.rest_framework import DjangoFilterBackend
from django.contrib.auth import get_user_model
from django.db.models import Q
from .models import Account, Beneficiary
from .serializers import (
    AccountSerializer, BeneficiarySerializer, AccountCreateSerializer,
    AccountStatusSerializer, AccountBalanceSerializer
)
from apps.users.permissions import (
    IsOwnerOrStaffAbove, IsAuthenticated, IsStaffOrAbove,
    IsAdminOrAbove, IsManagerOrAbove
)
from apps.utils.views import (
    StandardListAPIView, 
    StandardRetrieveAPIView, 
    StandardListCreateAPIView,
    StandardRetrieveUpdateDestroyAPIView,
    FilteredListMixin,
    SoftDeleteMixin
)
from apps.utils.response import APIResponse

User = get_user_model()


class AccountListView(StandardListAPIView, FilteredListMixin):
    """
    List user's accounts with proper filtering for mobile app.
    
    Supports filtering by:
    - account_type: Filter by account type (savings, checking, business)
    - status: Filter by account status (active, inactive, suspended, closed)
    - search: Search by account number or account type
    """
    serializer_class = AccountSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['account_type', 'status', 'currency']
    search_fields = ['account_number', 'account_type']
    ordering_fields = ['created_at', 'balance', 'account_type']
    ordering = ['-created_at']
    
    def get_queryset(self):
        user = self.request.user
        
        if user.is_staff_member:
            # Staff can see all accounts
            queryset = Account.objects.all()
        else:
            # Customers can only see their own active accounts
            queryset = Account.objects.filter(user=user)
        
        # Additional filtering for mobile app
        # Only show active accounts by default for customers
        if user.role == User.Role.CUSTOMER:
            queryset = queryset.filter(status=Account.Status.ACTIVE)
        
        return queryset.select_related('user')


class AccountDetailView(StandardRetrieveAPIView):
    """
    Retrieve individual account information for mobile app.
    """
    queryset = Account.objects.all()
    serializer_class = AccountSerializer
    permission_classes = [IsOwnerOrStaffAbove]
    
    def get_queryset(self):
        user = self.request.user
        
        if user.is_staff_member:
            return Account.objects.all()
        else:
            # Customers can only access their own accounts
            return Account.objects.filter(user=user)


class BeneficiaryListCreateView(StandardListCreateAPIView, FilteredListMixin):
    """
    List and create beneficiaries for mobile app.
    
    Supports filtering by:
    - is_active: Filter by active status
    - bank_name: Filter by bank name
    - search: Search by beneficiary name or account number
    """
    serializer_class = BeneficiarySerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['is_active', 'bank_name']
    search_fields = ['name', 'account_number', 'bank_name']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']
    
    def get_queryset(self):
        user = self.request.user
        
        if user.is_staff_member:
            # Staff can see all beneficiaries
            return Beneficiary.objects.all()
        else:
            # Customers can only see their own beneficiaries
            return Beneficiary.objects.filter(user=user, is_active=True)
    
    def perform_create(self, serializer):
        """Set the user to the current user when creating a beneficiary."""
        serializer.save(user=self.request.user)


class BeneficiaryDetailView(StandardRetrieveUpdateDestroyAPIView, SoftDeleteMixin):
    """
    Retrieve, update, and delete individual beneficiary for mobile app.
    """
    queryset = Beneficiary.objects.all()
    serializer_class = BeneficiarySerializer
    permission_classes = [IsOwnerOrStaffAbove]
    
    def get_queryset(self):
        user = self.request.user
        
        if user.is_staff_member:
            return Beneficiary.objects.all()
        else:
            # Customers can only access their own beneficiaries
            return Beneficiary.objects.filter(user=user)
    
    def perform_destroy(self, instance):
        """Soft delete beneficiary by setting is_active to False."""
        instance.is_active = False
        instance.save()


class AccountCreateView(generics.CreateAPIView):
    """
    Create new accounts with role-based permissions.

    - Customers can create savings/checking accounts for themselves
    - Staff+ can create accounts for any user
    - Manager+ required for business accounts
    """
    serializer_class = AccountCreateSerializer
    permission_classes = [IsAuthenticated]

    def create(self, request, *args, **kwargs):
        """Create account with comprehensive validation."""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            account = serializer.save()

            # Return the created account using the standard AccountSerializer
            response_serializer = AccountSerializer(account, context={'request': request})

            return Response(
                {
                    'success': True,
                    'message': 'Account created successfully',
                    'data': response_serializer.data
                },
                status=status.HTTP_201_CREATED
            )

        except Exception as e:
            return Response(
                {
                    'success': False,
                    'message': 'Failed to create account',
                    'error': str(e)
                },
                status=status.HTTP_400_BAD_REQUEST
            )


class AccountStatusUpdateView(generics.UpdateAPIView):
    """
    Update account status (staff+ only).

    Supports status changes:
    - Active ↔ Inactive (Staff+)
    - Active/Inactive → Suspended (Staff+)
    - Any status → Closed (Admin+)
    """
    queryset = Account.objects.all()
    serializer_class = AccountStatusSerializer
    permission_classes = [IsStaffOrAbove]

    def get_queryset(self):
        """Filter accounts based on user role."""
        user = self.request.user

        if user.is_admin_or_above:
            return Account.objects.all()
        elif user.is_staff_member:
            # Staff can only manage active customer accounts
            return Account.objects.filter(
                user__role=User.Role.CUSTOMER,
                status__in=[Account.Status.ACTIVE, Account.Status.INACTIVE, Account.Status.SUSPENDED]
            )

        return Account.objects.none()

    def update(self, request, *args, **kwargs):
        """Update account status with audit logging."""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)

        # Store old status for audit
        old_status = instance.status
        new_status = serializer.validated_data.get('status')
        status_reason = serializer.validated_data.get('status_reason', '')

        # Perform the update
        self.perform_update(serializer)

        # TODO: Create audit log entry
        # This would typically log the status change for compliance

        return Response(
            {
                'success': True,
                'message': f'Account status updated from {old_status} to {new_status}',
                'data': {
                    'id': str(instance.id),
                    'account_number': instance.account_number,
                    'old_status': old_status,
                    'new_status': new_status,
                    'updated_by': request.user.email,
                    'reason': status_reason
                }
            },
            status=status.HTTP_200_OK
        )


class AccountBalanceUpdateView(generics.UpdateAPIView):
    """
    Update account balance for admin operations (admin+ only).

    Used for:
    - Manual balance adjustments
    - Error corrections
    - Administrative credits/debits
    """
    queryset = Account.objects.all()
    serializer_class = AccountBalanceSerializer
    permission_classes = [IsAdminOrAbove]

    def get_queryset(self):
        """Admin+ can adjust any account balance."""
        return Account.objects.filter(
            status__in=[Account.Status.ACTIVE, Account.Status.INACTIVE]
        )

    def update(self, request, *args, **kwargs):
        """Update account balance with comprehensive audit trail."""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)

        # Store adjustment details for audit
        old_balance = instance.balance
        adjustment_amount = serializer.validated_data.get('adjustment_amount')
        adjustment_reason = serializer.validated_data.get('adjustment_reason')

        # Perform the update
        self.perform_update(serializer)

        # Refresh instance to get updated balance
        instance.refresh_from_db()
        new_balance = instance.balance

        # TODO: Create detailed audit log entry
        # This would typically create a transaction record and audit log

        return Response(
            {
                'success': True,
                'message': 'Account balance updated successfully',
                'data': {
                    'id': str(instance.id),
                    'account_number': instance.account_number,
                    'old_balance': str(old_balance),
                    'adjustment_amount': str(adjustment_amount),
                    'new_balance': str(new_balance),
                    'adjusted_by': request.user.email,
                    'reason': adjustment_reason,
                    'timestamp': instance.updated_at.isoformat()
                }
            },
            status=status.HTTP_200_OK
        )


class AccountCloseView(generics.UpdateAPIView):
    """
    Close/deactivate accounts (admin+ only).

    Supports:
    - Temporary deactivation (can be reactivated)
    - Permanent closure (cannot be reopened)
    """
    queryset = Account.objects.all()
    serializer_class = AccountStatusSerializer
    permission_classes = [IsAdminOrAbove]

    def get_queryset(self):
        """Admin+ can close any account except already closed ones."""
        return Account.objects.exclude(status=Account.Status.CLOSED)

    def update(self, request, *args, **kwargs):
        """Close account with validation and audit trail."""
        instance = self.get_object()

        # Validate closure request
        closure_type = request.data.get('closure_type', 'deactivate')  # 'deactivate' or 'close'
        closure_reason = request.data.get('status_reason', '')

        if not closure_reason:
            return Response(
                {
                    'success': False,
                    'message': 'Closure reason is required',
                    'error': 'status_reason field is mandatory for account closure'
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        # Determine new status
        if closure_type == 'close':
            new_status = Account.Status.CLOSED
            if not request.user.is_admin_or_above:
                return Response(
                    {
                        'success': False,
                        'message': 'Insufficient permissions',
                        'error': 'Only administrators can permanently close accounts'
                    },
                    status=status.HTTP_403_FORBIDDEN
                )
        else:
            new_status = Account.Status.INACTIVE

        # Check for pending transactions or outstanding balance
        if instance.balance != 0 and closure_type == 'close':
            return Response(
                {
                    'success': False,
                    'message': 'Cannot close account with outstanding balance',
                    'error': f'Account has balance of ${instance.balance}. Please clear balance before closure.'
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        # Store old status for audit
        old_status = instance.status

        # Update account status
        serializer_data = {
            'status': new_status,
            'status_reason': closure_reason
        }

        serializer = self.get_serializer(instance, data=serializer_data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        # TODO: Additional closure operations
        # - Notify user via email
        # - Create audit log entry
        # - Handle automatic transfers if needed
        # - Update related beneficiaries

        action_word = "closed" if closure_type == 'close' else "deactivated"

        return Response(
            {
                'success': True,
                'message': f'Account {action_word} successfully',
                'data': {
                    'id': str(instance.id),
                    'account_number': instance.account_number,
                    'old_status': old_status,
                    'new_status': new_status,
                    'closure_type': closure_type,
                    'reason': closure_reason,
                    'closed_by': request.user.email,
                    'timestamp': instance.updated_at.isoformat()
                }
            },
            status=status.HTTP_200_OK
        )
