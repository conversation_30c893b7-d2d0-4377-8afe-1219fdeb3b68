/**
 * Security Utilities for ExoBank Frontend Applications
 * 
 * Enhanced security features including secure storage, session management,
 * and security monitoring for authentication systems.
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { AuthUser, AuthSession, AuthEvent } from './authTypes';
import { validateJWTToken, getTokenExpirationTime } from './jwtUtils';

/**
 * Security configuration
 */
export const SECURITY_CONFIG = {
  // Session timeout (24 hours of inactivity)
  SESSION_TIMEOUT_MS: 24 * 60 * 60 * 1000,
  
  // Maximum failed login attempts before lockout
  MAX_FAILED_ATTEMPTS: 5,
  
  // Lockout duration (15 minutes)
  LOCKOUT_DURATION_MS: 15 * 60 * 1000,
  
  // Token refresh threshold (5 minutes before expiration)
  TOKEN_REFRESH_THRESHOLD_MS: 5 * 60 * 1000,
  
  // Security event logging
  ENABLE_SECURITY_LOGGING: true,
  
  // Biometric authentication timeout
  BIOMETRIC_TIMEOUT_MS: 30 * 1000,
} as const;

/**
 * Security event types
 */
export enum SecurityEvent {
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILED = 'LOGIN_FAILED',
  LOGOUT = 'LOGOUT',
  TOKEN_REFRESHED = 'TOKEN_REFRESHED',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  SESSION_TIMEOUT = 'SESSION_TIMEOUT',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  BIOMETRIC_AUTH_SUCCESS = 'BIOMETRIC_AUTH_SUCCESS',
  BIOMETRIC_AUTH_FAILED = 'BIOMETRIC_AUTH_FAILED',
}

/**
 * Security event data
 */
export interface SecurityEventData {
  event: SecurityEvent;
  timestamp: number;
  userId?: string;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Failed login attempt tracking
 */
interface FailedAttempt {
  email: string;
  attempts: number;
  lastAttempt: number;
  lockedUntil?: number;
}

/**
 * Session metadata for security tracking
 */
export interface SessionMetadata {
  createdAt: number;
  lastActivity: number;
  ipAddress?: string;
  userAgent?: string;
  deviceId?: string;
}

/**
 * Secure storage manager for authentication data
 */
export class SecureStorage {
  private static readonly STORAGE_PREFIX = 'exobank_secure_';
  
  /**
   * Store data securely with encryption (basic implementation)
   */
  static async setSecureItem(key: string, value: string): Promise<void> {
    try {
      const secureKey = this.STORAGE_PREFIX + key;
      // In a production app, you would encrypt the value here
      await AsyncStorage.setItem(secureKey, value);
    } catch (error) {
      console.error('Error storing secure item:', error);
      throw new Error('Failed to store secure data');
    }
  }
  
  /**
   * Retrieve data securely with decryption
   */
  static async getSecureItem(key: string): Promise<string | null> {
    try {
      const secureKey = this.STORAGE_PREFIX + key;
      const value = await AsyncStorage.getItem(secureKey);
      // In a production app, you would decrypt the value here
      return value;
    } catch (error) {
      console.error('Error retrieving secure item:', error);
      return null;
    }
  }
  
  /**
   * Remove secure item
   */
  static async removeSecureItem(key: string): Promise<void> {
    try {
      const secureKey = this.STORAGE_PREFIX + key;
      await AsyncStorage.removeItem(secureKey);
    } catch (error) {
      console.error('Error removing secure item:', error);
    }
  }
  
  /**
   * Clear all secure items
   */
  static async clearAllSecureItems(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const secureKeys = keys.filter(key => key.startsWith(this.STORAGE_PREFIX));
      await AsyncStorage.multiRemove(secureKeys);
    } catch (error) {
      console.error('Error clearing secure items:', error);
    }
  }
}

/**
 * Security event logger
 */
export class SecurityLogger {
  private static events: SecurityEventData[] = [];
  private static readonly MAX_EVENTS = 100;
  
  /**
   * Log security event
   */
  static async logEvent(event: SecurityEvent, details?: any, userId?: string): Promise<void> {
    if (!SECURITY_CONFIG.ENABLE_SECURITY_LOGGING) {
      return;
    }
    
    const eventData: SecurityEventData = {
      event,
      timestamp: Date.now(),
      userId,
      details,
      // In a real app, you would get actual IP and user agent
      ipAddress: 'unknown',
      userAgent: 'mobile-app',
    };
    
    this.events.push(eventData);
    
    // Keep only the last MAX_EVENTS
    if (this.events.length > this.MAX_EVENTS) {
      this.events = this.events.slice(-this.MAX_EVENTS);
    }
    
    // Store events persistently
    try {
      await SecureStorage.setSecureItem('security_events', JSON.stringify(this.events));
    } catch (error) {
      console.error('Error storing security events:', error);
    }
    
    // In a production app, you would also send critical events to a security monitoring service
    if (this.isCriticalEvent(event)) {
      console.warn('Critical security event:', eventData);
    }
  }
  
  /**
   * Get security events
   */
  static async getEvents(): Promise<SecurityEventData[]> {
    try {
      const eventsJson = await SecureStorage.getSecureItem('security_events');
      if (eventsJson) {
        this.events = JSON.parse(eventsJson);
      }
      return [...this.events];
    } catch (error) {
      console.error('Error retrieving security events:', error);
      return [];
    }
  }
  
  /**
   * Clear security events
   */
  static async clearEvents(): Promise<void> {
    this.events = [];
    await SecureStorage.removeSecureItem('security_events');
  }
  
  /**
   * Check if event is critical
   */
  private static isCriticalEvent(event: SecurityEvent): boolean {
    return [
      SecurityEvent.SUSPICIOUS_ACTIVITY,
      SecurityEvent.TOKEN_EXPIRED,
      SecurityEvent.SESSION_TIMEOUT,
    ].includes(event);
  }
}

/**
 * Failed login attempt manager
 */
export class FailedLoginManager {
  private static readonly STORAGE_KEY = 'failed_attempts';
  
  /**
   * Record failed login attempt
   */
  static async recordFailedAttempt(email: string): Promise<boolean> {
    try {
      const attempts = await this.getFailedAttempts();
      const now = Date.now();
      
      let attempt = attempts.find(a => a.email === email);
      
      if (!attempt) {
        attempt = {
          email,
          attempts: 0,
          lastAttempt: now,
        };
        attempts.push(attempt);
      }
      
      // Check if still locked
      if (attempt.lockedUntil && now < attempt.lockedUntil) {
        return false; // Still locked
      }
      
      // Reset if lockout period has passed
      if (attempt.lockedUntil && now >= attempt.lockedUntil) {
        attempt.attempts = 0;
        attempt.lockedUntil = undefined;
      }
      
      attempt.attempts++;
      attempt.lastAttempt = now;
      
      // Lock account if max attempts reached
      if (attempt.attempts >= SECURITY_CONFIG.MAX_FAILED_ATTEMPTS) {
        attempt.lockedUntil = now + SECURITY_CONFIG.LOCKOUT_DURATION_MS;
        
        await SecurityLogger.logEvent(SecurityEvent.SUSPICIOUS_ACTIVITY, {
          reason: 'Max failed login attempts reached',
          email,
          attempts: attempt.attempts,
        });
      }
      
      await SecureStorage.setSecureItem(this.STORAGE_KEY, JSON.stringify(attempts));
      
      return attempt.lockedUntil ? false : true; // Return false if locked
    } catch (error) {
      console.error('Error recording failed attempt:', error);
      return true; // Allow attempt on error
    }
  }
  
  /**
   * Clear failed attempts for email
   */
  static async clearFailedAttempts(email: string): Promise<void> {
    try {
      const attempts = await this.getFailedAttempts();
      const filtered = attempts.filter(a => a.email !== email);
      await SecureStorage.setSecureItem(this.STORAGE_KEY, JSON.stringify(filtered));
    } catch (error) {
      console.error('Error clearing failed attempts:', error);
    }
  }
  
  /**
   * Check if email is locked
   */
  static async isLocked(email: string): Promise<boolean> {
    try {
      const attempts = await this.getFailedAttempts();
      const attempt = attempts.find(a => a.email === email);
      
      if (!attempt || !attempt.lockedUntil) {
        return false;
      }
      
      const now = Date.now();
      return now < attempt.lockedUntil;
    } catch (error) {
      console.error('Error checking lock status:', error);
      return false;
    }
  }
  
  /**
   * Get failed attempts
   */
  private static async getFailedAttempts(): Promise<FailedAttempt[]> {
    try {
      const attemptsJson = await SecureStorage.getSecureItem(this.STORAGE_KEY);
      return attemptsJson ? JSON.parse(attemptsJson) : [];
    } catch (error) {
      console.error('Error getting failed attempts:', error);
      return [];
    }
  }
}

/**
 * Session manager for enhanced session security
 */
export class SessionManager {
  private static readonly METADATA_KEY = 'session_metadata';
  private static sessionTimeout: NodeJS.Timeout | null = null;

  /**
   * Create session with metadata
   */
  static async createSession(session: AuthSession, metadata?: Partial<SessionMetadata>): Promise<void> {
    const sessionMetadata: SessionMetadata = {
      createdAt: Date.now(),
      lastActivity: Date.now(),
      ipAddress: metadata?.ipAddress || 'unknown',
      userAgent: metadata?.userAgent || 'mobile-app',
      deviceId: metadata?.deviceId || 'unknown',
    };

    try {
      await SecureStorage.setSecureItem(this.METADATA_KEY, JSON.stringify(sessionMetadata));

      // Start session timeout monitoring
      this.startSessionTimeout();

      await SecurityLogger.logEvent(SecurityEvent.LOGIN_SUCCESS, {
        userId: session.user.id,
        metadata: sessionMetadata,
      }, session.user.id);
    } catch (error) {
      console.error('Error creating session:', error);
    }
  }

  /**
   * Update session activity
   */
  static async updateActivity(): Promise<void> {
    try {
      const metadata = await this.getSessionMetadata();
      if (metadata) {
        metadata.lastActivity = Date.now();
        await SecureStorage.setSecureItem(this.METADATA_KEY, JSON.stringify(metadata));

        // Reset session timeout
        this.startSessionTimeout();
      }
    } catch (error) {
      console.error('Error updating session activity:', error);
    }
  }

  /**
   * Check if session is valid (not timed out)
   */
  static async isSessionValid(): Promise<boolean> {
    try {
      const metadata = await this.getSessionMetadata();
      if (!metadata) {
        return false;
      }

      const now = Date.now();
      const timeSinceLastActivity = now - metadata.lastActivity;

      return timeSinceLastActivity < SECURITY_CONFIG.SESSION_TIMEOUT_MS;
    } catch (error) {
      console.error('Error checking session validity:', error);
      return false;
    }
  }

  /**
   * Clear session
   */
  static async clearSession(): Promise<void> {
    try {
      await SecureStorage.removeSecureItem(this.METADATA_KEY);

      if (this.sessionTimeout) {
        clearTimeout(this.sessionTimeout);
        this.sessionTimeout = null;
      }

      await SecurityLogger.logEvent(SecurityEvent.LOGOUT);
    } catch (error) {
      console.error('Error clearing session:', error);
    }
  }

  /**
   * Get session metadata
   */
  static async getSessionMetadata(): Promise<SessionMetadata | null> {
    try {
      const metadataJson = await SecureStorage.getSecureItem(this.METADATA_KEY);
      return metadataJson ? JSON.parse(metadataJson) : null;
    } catch (error) {
      console.error('Error getting session metadata:', error);
      return null;
    }
  }

  /**
   * Start session timeout monitoring
   */
  private static startSessionTimeout(): void {
    if (this.sessionTimeout) {
      clearTimeout(this.sessionTimeout);
    }

    this.sessionTimeout = setTimeout(async () => {
      await SecurityLogger.logEvent(SecurityEvent.SESSION_TIMEOUT);
      // In a real app, you would trigger a logout here
      console.warn('Session timeout detected');
    }, SECURITY_CONFIG.SESSION_TIMEOUT_MS);
  }
}
