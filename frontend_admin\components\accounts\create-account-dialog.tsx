"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import adminApiService from "@/lib/adminApiService"
import { getCurrentUser } from "@/lib/auth"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Loader2 } from "lucide-react"
import { toast } from "@/hooks/use-toast"

interface CreateAccountDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CreateAccountDialog({ open, onOpenChange }: CreateAccountDialogProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    customerName: "",
    email: "",
    phone: "",
    address: "",
    accountType: "",
    initialDeposit: "",
    notes: "",
  })

  const router = useRouter()
  const currentUser = getCurrentUser()

  // Role-based permissions
  const canCreateAccounts = currentUser && ['staff', 'manager', 'admin', 'super_admin'].includes(currentUser.role)
  const canCreateBusinessAccounts = currentUser && ['manager', 'admin', 'super_admin'].includes(currentUser.role)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Check if user has permission to create accounts
      if (!currentUser || !['staff', 'manager', 'admin', 'super_admin'].includes(currentUser.role)) {
        throw new Error('Insufficient permissions to create accounts')
      }

      // Validate required fields
      if (!formData.customerName || !formData.email || !formData.accountType) {
        throw new Error('Please fill in all required fields')
      }

      // Check if business account creation requires manager+ role
      if (formData.accountType === 'business' && !['manager', 'admin', 'super_admin'].includes(currentUser.role)) {
        throw new Error('Business accounts require manager approval. Please contact your manager.')
      }

      // Split customer name into first and last name
      const nameParts = formData.customerName.trim().split(' ')
      const firstName = nameParts[0] || ''
      const lastName = nameParts.slice(1).join(' ') || ''

      // Prepare account creation data
      const accountData = {
        account_type: formData.accountType as 'savings' | 'checking' | 'business',
        initial_balance: formData.initialDeposit ? parseFloat(formData.initialDeposit) : 0,
        user_email: formData.email,
        user_first_name: firstName,
        user_last_name: lastName,
        user_phone: formData.phone || undefined,
        user_address: formData.address || undefined,
        notes: formData.notes || undefined
      }

      // Call Django API to create account
      const response = await adminApiService.createAccount(accountData)

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to create account')
      }

      toast({
        title: "Account Created",
        description: `New ${formData.accountType} account has been successfully created for ${formData.customerName}.`,
      })

      // Reset form and close dialog
      setFormData({
        customerName: "",
        email: "",
        phone: "",
        address: "",
        accountType: "",
        initialDeposit: "",
        notes: "",
      })
      onOpenChange(false)
      router.refresh()

    } catch (error: any) {
      console.error('Account creation error:', error)

      let errorMessage = "Failed to create account"

      // Handle specific error types
      if (error.message?.includes('permission')) {
        errorMessage = error.message
      } else if (error.message?.includes('required fields')) {
        errorMessage = error.message
      } else if (error.message?.includes('Business accounts')) {
        errorMessage = error.message
      } else if (error.details) {
        // Handle field-specific validation errors from Django
        const fieldErrors = Object.entries(error.details)
          .map(([field, errors]) => `${field}: ${Array.isArray(errors) ? errors.join(', ') : errors}`)
          .join('; ')
        errorMessage = fieldErrors || errorMessage
      } else if (error.message) {
        errorMessage = error.message
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // Show permission error if user doesn't have access
  if (!canCreateAccounts) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>Access Denied</DialogTitle>
            <DialogDescription>
              You don't have permission to create accounts. Please contact your administrator.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button onClick={() => onOpenChange(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Account</DialogTitle>
          <DialogDescription>
            Create a new bank account for a customer. Fill in all required information.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            {/* Customer Information */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-900">Customer Information</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="customerName">Full Name *</Label>
                  <Input
                    id="customerName"
                    value={formData.customerName}
                    onChange={(e) => setFormData({ ...formData, customerName: e.target.value })}
                    placeholder="John Doe"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                    placeholder="+****************"
                  />
                </div>
                <div>
                  <Label htmlFor="accountType">Account Type *</Label>
                  <Select
                    value={formData.accountType}
                    onValueChange={(value) => setFormData({ ...formData, accountType: value })}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select account type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="checking">Checking Account</SelectItem>
                      <SelectItem value="savings">Savings Account</SelectItem>
                      {canCreateBusinessAccounts && (
                        <SelectItem value="business">Business Account</SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  {!canCreateBusinessAccounts && (
                    <p className="text-xs text-muted-foreground">
                      Business accounts require manager approval
                    </p>
                  )}
                </div>
              </div>
              <div>
                <Label htmlFor="address">Address</Label>
                <Textarea
                  id="address"
                  value={formData.address}
                  onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                  placeholder="123 Main St, City, State, ZIP"
                  rows={2}
                />
              </div>
            </div>

            {/* Account Details */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-900">Account Details</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="initialDeposit">Initial Deposit</Label>
                  <Input
                    id="initialDeposit"
                    type="number"
                    step="0.01"
                    value={formData.initialDeposit}
                    onChange={(e) => setFormData({ ...formData, initialDeposit: e.target.value })}
                    placeholder="0.00"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                  placeholder="Additional notes or comments..."
                  rows={3}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Account
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
