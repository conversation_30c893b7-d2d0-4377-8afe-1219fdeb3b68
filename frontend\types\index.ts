// Backend API Account type (matches Django API response)
export interface Account {
  id: string;
  user: string;
  user_email: string;
  user_name: string;
  account_number: string;
  account_type: 'savings' | 'checking' | 'business';
  balance: string;
  balance_display: string;
  currency: string;
  status: 'active' | 'inactive' | 'suspended' | 'closed';
  created_at: string;
  updated_at: string;
  // Additional fields for UI state
  isVisible?: boolean;
}

// Frontend display Account type (for backward compatibility with existing components)
export interface DisplayAccount {
  id: string;
  type: 'Savings' | 'Checking' | 'Business';
  accountNumber: string;
  balance: number;
  interestRate?: number;
  accruedInterest?: number;
  isVisible: boolean;
  status?: string;
  currency?: string;
}

// Backend API Transaction type (matches Django API response)
export interface Transaction {
  id: string;
  user: string;
  user_email: string;
  from_account: string;
  from_account_number: string;
  from_account_type: string;
  to_account: string | null;
  to_account_number: string | null;
  to_account_type: string | null;
  transaction_type: 'deposit' | 'withdrawal' | 'transfer' | 'payment';
  transaction_type_display: string;
  amount: string;
  amount_display: string;
  currency: string;
  description: string;
  reference_number: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  status_display: string;
  created_at: string;
  updated_at: string;
  processed_at: string | null;
}

// Frontend display Transaction type (for backward compatibility)
export interface DisplayTransaction {
  id: string;
  accountId: string;
  date: string;
  amount: number;
  type: 'credit' | 'debit';
  description: string;
  category: string;
  status?: string;
}

export interface FinancialProduct {
  id: string;
  title: string;
  description: string;
  icon: string;
  route: string;
}

// Backend API User type (matches Django API response)
export interface User {
  id: string;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  phone_number: string;
  is_active: boolean;
  date_joined: string;
  profile?: {
    avatar_url?: string;
    date_of_birth?: string;
    address?: string;
    city?: string;
    country?: string;
  };
}

// Frontend display User type (for backward compatibility)
export interface DisplayUser {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
}

export interface TransferData {
  id: string;
  amount: number;
  currency: string;
  sourceAccountId: string;
  destinationAccountId?: string;
  recipientName?: string;
  recipientAccountNumber?: string;
  recipientBankName?: string;
  ifscCode?: string;
  description: string;
  transferDate: string;
  status: 'pending' | 'completed' | 'failed' | 'scheduled';
  transactionId: string;
  createdAt: string;
}

// Network Configuration Types
export type PlatformType = 'android-emulator' | 'android-device' | 'ios-simulator' | 'ios-device' | 'web' | 'windows';

export interface NetworkInterface {
  name: string;
  address: string;
  family: 'IPv4' | 'IPv6';
  internal: boolean;
  mac?: string;
}

export interface NetworkConfig {
  platform: PlatformType;
  deviceType: 'emulator' | 'simulator' | 'physical' | 'browser' | 'desktop';
  metroBundlerUrl: string;
  backendApiUrl: string;
  localNetworkIp?: string;
  fallbackUrls: {
    metroBundler: string[];
    backendApi: string[];
  };
  diagnostics: NetworkDiagnostics;
}

export interface NetworkDiagnostics {
  networkInterfaces: NetworkInterface[];
  connectivity: any; // NetInfoState from @react-native-community/netinfo
  platformInfo: PlatformInfo;
  environmentInfo: EnvironmentInfo;
  detectionTimestamp: number;
}

export interface PlatformInfo {
  os: string;
  version: string;
  isEmulator: boolean;
  isSimulator: boolean;
  deviceModel?: string;
  deviceName?: string;
}

export interface EnvironmentInfo {
  isDevelopment: boolean;
  expoVersion?: string;
  reactNativeVersion?: string;
  nodeEnv?: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  details?: any;
}

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// Beneficiary types
export interface Beneficiary {
  id: string;
  user: string;
  name: string;
  account_number: string;
  bank_name: string;
  routing_number: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Type conversion utilities
export class TypeConverter {
  static accountToDisplay(account: Account): DisplayAccount {
    return {
      id: account.id,
      type: account.account_type === 'savings' ? 'Savings' :
            account.account_type === 'checking' ? 'Checking' : 'Business',
      accountNumber: account.account_number,
      balance: parseFloat(account.balance),
      isVisible: account.isVisible ?? true,
      status: account.status,
      currency: account.currency,
    };
  }

  static transactionToDisplay(transaction: Transaction): DisplayTransaction {
    return {
      id: transaction.id,
      accountId: transaction.from_account,
      date: transaction.created_at,
      amount: parseFloat(transaction.amount),
      type: transaction.transaction_type === 'deposit' ? 'credit' : 'debit',
      description: transaction.description,
      category: transaction.transaction_type_display,
      status: transaction.status,
    };
  }

  static userToDisplay(user: User): DisplayUser {
    return {
      id: user.id,
      name: `${user.first_name} ${user.last_name}`.trim(),
      email: user.email,
      phone: user.phone_number,
      avatar: user.profile?.avatar_url,
    };
  }
}