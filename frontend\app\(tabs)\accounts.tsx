import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { ChevronRight } from 'lucide-react-native';
import { router } from 'expo-router';
import AccountCard from '@/components/AccountCard';
import SafeAreaView from '@/components/SafeAreaView';
import { Account, DisplayAccount, TypeConverter } from '@/types';
import { useTheme } from '@/hooks/useTheme';
import apiService from '@/services/apiService';
import { LoadingIndicator, AutoLoadingIndicator } from '@/components/ui/LoadingIndicator';
import { useApiLoading } from '@/hooks/useLoading';
import enhancedErrorService from '@/services/enhancedErrorService';

export default function AccountsScreen() {
  const { theme } = useTheme();
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [displayAccounts, setDisplayAccounts] = useState<DisplayAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { executeWithLoading, isOperationLoading } = useApiLoading();

  // Fetch accounts from API
  const fetchAccounts = async () => {
    try {
      setError(null);
      const response = await executeWithLoading('fetch-accounts', async () => {
        return await apiService.getAccounts();
      }, {
        operation: 'fetch-accounts',
        message: 'Loading your accounts...'
      });

      if (response.success && response.data) {
        setAccounts(response.data);
        // Convert to display format for UI components
        const convertedAccounts = response.data.map(TypeConverter.accountToDisplay);
        setDisplayAccounts(convertedAccounts);
      } else {
        throw new Error(response.error || 'Failed to load accounts');
      }
    } catch (err: any) {
      console.error('Error fetching accounts:', err);
      setError(err.message || 'Failed to load accounts');

      // Use enhanced error handling
      await enhancedErrorService.handleError(err, {
        showAlert: true,
        retryCallback: fetchAccounts,
        context: { operation: 'fetchAccounts' }
      });
    } finally {
      setLoading(false);
    }
  };

  // Load accounts on component mount
  useEffect(() => {
    fetchAccounts();
  }, []);

  const handleToggleVisibility = (accountId: string) => {
    setDisplayAccounts(prev =>
      prev.map(account =>
        account.id === accountId
          ? { ...account, isVisible: !account.isVisible }
          : account
      )
    );
  };

  const handleAccountPress = (account: DisplayAccount) => {
    router.push(`/account-details?id=${account.id}`);
  };

  const getTotalBalance = () => {
    return displayAccounts
      .filter(account => account.type !== 'Loan')
      .reduce((total, account) => total + account.balance, 0);
  };

  const getTotalLoan = () => {
    return displayAccounts
      .filter(account => account.type === 'Loan')
      .reduce((total, account) => total + Math.abs(account.balance), 0);
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.background,
    },
    header: {
      padding: 20,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.text,
    },
    summaryContainer: {
      flexDirection: 'row',
      paddingHorizontal: 20,
      marginBottom: 24,
      gap: 12,
    },
    summaryCard: {
      flex: 1,
      borderRadius: 12,
      padding: 16,
    },
    balanceCard: {
      backgroundColor: theme.primary,
    },
    loanCard: {
      backgroundColor: theme.secondary,
    },
    summaryLabel: {
      fontSize: 14,
      color: theme.textOnPrimary,
      opacity: 0.9,
      marginBottom: 8,
    },
    loanLabel: {
      color: theme.textOnPrimary,
    },
    summaryAmount: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.textOnPrimary,
    },
    loanAmount: {
      color: theme.textOnPrimary,
    },
    accountsSection: {
      marginBottom: 24,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.text,
      marginBottom: 16,
      paddingHorizontal: 20,
    },
    accountCardContainer: {
      marginBottom: 16,
    },
    cardWrapper: {
      alignItems: 'center',
    },
    detailsButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: 12,
      paddingVertical: 8,
    },
    detailsButtonText: {
      fontSize: 14,
      color: theme.primary,
      fontWeight: '600',
      marginRight: 4,
    },
    quickActions: {
      paddingHorizontal: 20,
      marginBottom: 24,
    },
    actionItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: 16,
      paddingHorizontal: 16,
      backgroundColor: theme.surface,
      borderRadius: 12,
      marginBottom: 8,
      elevation: 1,
      shadowColor: theme.shadow,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.22,
      shadowRadius: 2.22,
      borderWidth: 1,
      borderColor: theme.border,
      minHeight: 56,
    },
    actionText: {
      fontSize: 16,
      color: theme.text,
      fontWeight: '500',
    },
  });

  // Show loading indicator while fetching accounts
  if (loading && displayAccounts.length === 0) {
    return (
      <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
        <View style={styles.header}>
          <Text style={styles.title}>My Accounts</Text>
        </View>
        <LoadingIndicator 
          message="Loading your accounts..." 
          size="large"
          style={{ flex: 1, justifyContent: 'center' }}
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <Text style={styles.title}>My Accounts</Text>
        {/* Show loading indicator for refresh operations */}
        <AutoLoadingIndicator 
          operationKey="fetch-accounts" 
          size="small"
          style={{ position: 'absolute', right: 20, top: 20 }}
        />
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Summary Cards */}
        <View style={styles.summaryContainer}>
          <View style={[styles.summaryCard, styles.balanceCard]}>
            <Text style={styles.summaryLabel}>Total Balance</Text>
            <Text style={styles.summaryAmount}>
              ₹{getTotalBalance().toLocaleString('en-IN')}
            </Text>
          </View>
          
          <View style={[styles.summaryCard, styles.loanCard]}>
            <Text style={[styles.summaryLabel, styles.loanLabel]}>Total Loans</Text>
            <Text style={[styles.summaryAmount, styles.loanAmount]}>
              ₹{getTotalLoan().toLocaleString('en-IN')}
            </Text>
          </View>
        </View>

        {/* Account Cards */}
        <View style={styles.accountsSection}>
          <Text style={styles.sectionTitle}>All Accounts</Text>
          
          {displayAccounts.map((account) => (
            <View key={account.id} style={styles.accountCardContainer}>
              <View style={styles.cardWrapper}>
                <AccountCard
                  account={account}
                  onToggleVisibility={handleToggleVisibility}
                  onPress={handleAccountPress}
                />
              </View>

              <TouchableOpacity
                style={styles.detailsButton}
                onPress={() => handleAccountPress(account)}
              >
                <Text style={styles.detailsButtonText}>View Details</Text>
                <ChevronRight size={16} color={theme.primary} />
              </TouchableOpacity>
            </View>
          ))}
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          
          <TouchableOpacity
            style={styles.actionItem}
            onPress={() => router.push('/open-account')}
          >
            <Text style={styles.actionText}>Open New Account</Text>
            <ChevronRight size={16} color={theme.textSecondary} />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionItem}>
            <Text style={styles.actionText}>Account Statements</Text>
            <ChevronRight size={16} color={theme.textSecondary} />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionItem}>
            <Text style={styles.actionText}>Interest Certificates</Text>
            <ChevronRight size={16} color={theme.textSecondary} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}