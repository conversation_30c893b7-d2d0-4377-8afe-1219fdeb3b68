/**
 * Account Management Integration Test
 * 
 * This script tests the account management functionality
 * of the Next.js admin frontend with Django backend integration.
 */

const https = require('http')
const { URL } = require('url')

// Test configuration
const TEST_CONFIG = {
  API_BASE_URL: 'http://localhost:8000',
  TIMEOUT: 10000,
  CREDENTIALS: {
    admin: { email: '<EMAIL>', password: 'admin123' },
    manager: { email: '<EMAIL>', password: 'manager123' },
    staff: { email: '<EMAIL>', password: 'staff123' },
    customer: { email: '<EMAIL>', password: 'customer123' }
  }
}

/**
 * Make HTTP request
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const parsedUrl = new URL(url)
    
    const body = options.body ? JSON.stringify(options.body) : null
    
    const requestOptions = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port || (parsedUrl.protocol === 'https:' ? 443 : 80),
      path: parsedUrl.pathname + parsedUrl.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      timeout: TEST_CONFIG.TIMEOUT
    }

    if (body) {
      requestOptions.headers['Content-Length'] = Buffer.byteLength(body)
    }

    const req = https.request(requestOptions, (res) => {
      let data = ''
      
      res.on('data', (chunk) => {
        data += chunk
      })
      
      res.on('end', () => {
        try {
          const response = {
            status: res.statusCode,
            headers: res.headers,
            data: data ? JSON.parse(data) : null
          }
          resolve(response)
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data,
            parseError: error.message
          })
        }
      })
    })

    req.on('error', (error) => {
      reject(new Error(`Request failed: ${error.message}`))
    })

    req.on('timeout', () => {
      req.destroy()
      reject(new Error('Request timeout'))
    })

    if (body) {
      req.write(body)
    }
    
    req.end()
  })
}

/**
 * Authenticate user and get tokens
 */
async function authenticateUser(credentials) {
  try {
    const response = await makeRequest(`${TEST_CONFIG.API_BASE_URL}/api/auth/login/`, {
      method: 'POST',
      body: credentials
    })

    if (response.status === 200 && response.data?.success) {
      return {
        success: true,
        tokens: response.data.data,
        user: response.data.data.user
      }
    } else {
      return {
        success: false,
        error: response.data?.error?.message || 'Authentication failed',
        status: response.status
      }
    }
  } catch (error) {
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * Test account creation
 */
async function testAccountCreation(authToken, accountData) {
  try {
    const response = await makeRequest(`${TEST_CONFIG.API_BASE_URL}/api/v1/accounts/create/`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`
      },
      body: accountData
    })

    return {
      success: response.status === 201 && response.data?.success,
      status: response.status,
      data: response.data,
      account: response.data?.data
    }
  } catch (error) {
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * Test account update
 */
async function testAccountUpdate(authToken, accountId, updateData) {
  try {
    const response = await makeRequest(`${TEST_CONFIG.API_BASE_URL}/api/v1/accounts/${accountId}/`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${authToken}`
      },
      body: updateData
    })

    return {
      success: response.status === 200 && response.data?.success,
      status: response.status,
      data: response.data,
      account: response.data?.data
    }
  } catch (error) {
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * Test account listing
 */
async function testAccountListing(authToken) {
  try {
    const response = await makeRequest(`${TEST_CONFIG.API_BASE_URL}/api/v1/accounts/`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    })

    return {
      success: response.status === 200 && response.data?.success,
      status: response.status,
      data: response.data,
      accounts: response.data?.data?.results || response.data?.data
    }
  } catch (error) {
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * Run comprehensive account management tests
 */
async function runAccountManagementTests() {
  console.log('🚀 Starting Account Management Integration Tests...\n')

  const results = {
    passed: 0,
    failed: 0,
    tests: []
  }

  // Test 1: Admin Authentication
  console.log('📋 Test 1: Admin Authentication')
  const adminAuth = await authenticateUser(TEST_CONFIG.CREDENTIALS.admin)
  if (adminAuth.success) {
    console.log('✅ Admin authentication successful')
    console.log(`   User: ${adminAuth.user.email} (${adminAuth.user.role})`)
    results.passed++
  } else {
    console.log('❌ Admin authentication failed:', adminAuth.error)
    results.failed++
  }
  results.tests.push({ name: 'Admin Authentication', success: adminAuth.success })

  if (!adminAuth.success) {
    console.log('\n❌ Cannot continue tests without authentication')
    return results
  }

  // Test 2: Account Listing
  console.log('\n📋 Test 2: Account Listing')
  const accountList = await testAccountListing(adminAuth.tokens.access)
  if (accountList.success) {
    console.log('✅ Account listing successful')
    console.log(`   Found ${accountList.accounts?.length || 0} accounts`)
    results.passed++
  } else {
    console.log('❌ Account listing failed:', accountList.error)
    results.failed++
  }
  results.tests.push({ name: 'Account Listing', success: accountList.success })

  // Test 3: Account Creation
  console.log('\n📋 Test 3: Account Creation')
  const newAccountData = {
    account_type: 'savings',
    initial_balance: 100.00,
    user_email: '<EMAIL>',
    user_first_name: 'Test',
    user_last_name: 'User',
    notes: 'Test account created by integration test'
  }
  
  const accountCreation = await testAccountCreation(adminAuth.tokens.access, newAccountData)
  if (accountCreation.success) {
    console.log('✅ Account creation successful')
    console.log(`   Account Number: ${accountCreation.account?.account_number}`)
    console.log(`   Account Type: ${accountCreation.account?.account_type}`)
    console.log(`   Balance: ${accountCreation.account?.balance}`)
    results.passed++
  } else {
    console.log('❌ Account creation failed:', accountCreation.error || accountCreation.data?.error?.message)
    results.failed++
  }
  results.tests.push({ name: 'Account Creation', success: accountCreation.success })

  // Test 4: Account Update (if creation was successful)
  if (accountCreation.success && accountCreation.account) {
    console.log('\n📋 Test 4: Account Update')
    const updateData = {
      balance: '250.00',
      status: 'active'
    }
    
    const accountUpdate = await testAccountUpdate(
      adminAuth.tokens.access, 
      accountCreation.account.id, 
      updateData
    )
    
    if (accountUpdate.success) {
      console.log('✅ Account update successful')
      console.log(`   New Balance: ${accountUpdate.account?.balance}`)
      console.log(`   Status: ${accountUpdate.account?.status}`)
      results.passed++
    } else {
      console.log('❌ Account update failed:', accountUpdate.error || accountUpdate.data?.error?.message)
      results.failed++
    }
    results.tests.push({ name: 'Account Update', success: accountUpdate.success })
  }

  return results
}

/**
 * Main test execution
 */
async function main() {
  try {
    const results = await runAccountManagementTests()
    
    console.log('\n' + '='.repeat(50))
    console.log('📊 TEST RESULTS SUMMARY')
    console.log('='.repeat(50))
    console.log(`✅ Passed: ${results.passed}`)
    console.log(`❌ Failed: ${results.failed}`)
    console.log(`📈 Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`)
    
    console.log('\n📋 Individual Test Results:')
    results.tests.forEach(test => {
      console.log(`   ${test.success ? '✅' : '❌'} ${test.name}`)
    })
    
    if (results.failed === 0) {
      console.log('\n🎉 All tests passed! Account management integration is working correctly.')
      process.exit(0)
    } else {
      console.log('\n⚠️  Some tests failed. Please check the Django backend and database setup.')
      process.exit(1)
    }
    
  } catch (error) {
    console.error('\n💥 Test execution failed:', error.message)
    process.exit(1)
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  main()
}

module.exports = {
  runAccountManagementTests,
  authenticateUser,
  testAccountCreation,
  testAccountUpdate,
  testAccountListing
}
