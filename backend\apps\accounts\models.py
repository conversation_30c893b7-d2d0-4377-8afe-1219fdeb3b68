"""
Account models for ExoBank application.

This module defines banking account models with proper security
and role-based access controls.
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator
from decimal import Decimal
import uuid

User = get_user_model()


class Account(models.Model):
    """
    Banking account model for ExoBank users.
    """
    
    class AccountType(models.TextChoices):
        SAVINGS = 'savings', 'Savings Account'
        CHECKING = 'checking', 'Checking Account'
        BUSINESS = 'business', 'Business Account'
    
    class Status(models.TextChoices):
        ACTIVE = 'active', 'Active'
        INACTIVE = 'inactive', 'Inactive'
        SUSPENDED = 'suspended', 'Suspended'
        CLOSED = 'closed', 'Closed'
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='accounts')
    account_number = models.Char<PERSON>ield(max_length=20, unique=True)
    account_type = models.CharField(
        max_length=20,
        choices=AccountType.choices,
        default=AccountType.SAVINGS
    )
    balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))]
    )
    currency = models.CharField(max_length=3, default='USD')
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.ACTIVE
    )

    # Additional banking fields to match Supabase schema
    is_primary = models.BooleanField(
        default=False,
        help_text="Indicates if this is the user's primary account"
    )
    overdraft_limit = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text="Maximum overdraft amount allowed"
    )
    interest_rate = models.DecimalField(
        max_digits=5,
        decimal_places=4,
        default=Decimal('0.0000'),
        validators=[MinValueValidator(Decimal('0.0000'))],
        help_text="Annual interest rate as decimal (e.g., 0.0350 for 3.5%)"
    )
    minimum_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text="Minimum balance required for this account"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'accounts'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['account_number']),
            models.Index(fields=['status']),
        ]
    
    def __str__(self):
        return f"{self.account_number} - {self.user.email}"


class Beneficiary(models.Model):
    """
    Beneficiary model for money transfers.
    """

    class BeneficiaryType(models.TextChoices):
        PERSONAL = 'personal', 'Personal'
        BUSINESS = 'business', 'Business'

    class Status(models.TextChoices):
        ACTIVE = 'active', 'Active'
        INACTIVE = 'inactive', 'Inactive'
        PENDING_VERIFICATION = 'pending_verification', 'Pending Verification'

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='beneficiaries')
    name = models.CharField(max_length=100)
    account_number = models.CharField(max_length=20)
    bank_name = models.CharField(max_length=100)

    # Additional banking fields to match Supabase schema
    bank_code = models.CharField(max_length=20, blank=True, null=True)
    routing_number = models.CharField(max_length=20, blank=True, null=True)
    swift_code = models.CharField(max_length=20, blank=True, null=True)
    beneficiary_type = models.CharField(
        max_length=20,
        choices=BeneficiaryType.choices,
        default=BeneficiaryType.PERSONAL
    )
    relationship = models.CharField(max_length=50, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    address = models.TextField(blank=True, null=True)

    # Verification fields
    is_verified = models.BooleanField(default=False)
    verification_date = models.DateTimeField(blank=True, null=True)

    # Status field (replaces is_active for better granularity)
    status = models.CharField(
        max_length=30,
        choices=Status.choices,
        default=Status.ACTIVE
    )

    # Keep is_active for backward compatibility
    is_active = models.BooleanField(default=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'beneficiaries'
        ordering = ['name']
        unique_together = ['user', 'account_number']
    
    def __str__(self):
        return f"{self.name} - {self.account_number}"
