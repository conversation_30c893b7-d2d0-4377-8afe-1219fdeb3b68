# Generated by Django 4.2.7 on 2025-07-22 20:07

from decimal import Decimal
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0002_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='interest_rate',
            field=models.DecimalField(decimal_places=4, default=Decimal('0.0000'), help_text='Annual interest rate as decimal (e.g., 0.0350 for 3.5%)', max_digits=5, validators=[django.core.validators.MinValueValidator(Decimal('0.0000'))]),
        ),
        migrations.AddField(
            model_name='account',
            name='is_primary',
            field=models.BooleanField(default=False, help_text="Indicates if this is the user's primary account"),
        ),
        migrations.AddField(
            model_name='account',
            name='minimum_balance',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Minimum balance required for this account', max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))]),
        ),
        migrations.AddField(
            model_name='account',
            name='overdraft_limit',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Maximum overdraft amount allowed', max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))]),
        ),
        migrations.AddField(
            model_name='beneficiary',
            name='address',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='beneficiary',
            name='bank_code',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='beneficiary',
            name='beneficiary_type',
            field=models.CharField(choices=[('personal', 'Personal'), ('business', 'Business')], default='personal', max_length=20),
        ),
        migrations.AddField(
            model_name='beneficiary',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='beneficiary',
            name='is_verified',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='beneficiary',
            name='phone_number',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='beneficiary',
            name='relationship',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='beneficiary',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('pending_verification', 'Pending Verification')], default='active', max_length=30),
        ),
        migrations.AddField(
            model_name='beneficiary',
            name='swift_code',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='beneficiary',
            name='verification_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='beneficiary',
            name='routing_number',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
    ]
