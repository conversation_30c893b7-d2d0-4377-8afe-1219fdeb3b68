# ExoBank Account Management API Documentation

## Overview

This document describes the new account management endpoints implemented as part of Priority 2: Backend API Enhancements. These endpoints provide comprehensive account lifecycle management with role-based permissions and security controls.

## Base URL

All endpoints are prefixed with `/api/v1/accounts/`

## Authentication

All endpoints require authentication via JW<PERSON> token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Role-Based Access Control

The API enforces a 5-tier role hierarchy:
- **Super Admin**: Full system access
- **Admin**: User and account management, balance adjustments
- **Manager**: Department oversight, business account approval
- **Staff**: Customer support, account status management
- **Customer**: Personal account operations only

## New Endpoints

### 1. Create Account

**Endpoint**: `POST /api/v1/accounts/create/`

**Description**: Create new bank accounts with role-based validation.

**Permissions**:
- Customers: Can create savings/checking accounts for themselves
- Staff+: Can create accounts for any user
- Manager+: Required for business accounts

**Request Body**:
```json
{
    "user_id": "uuid (optional, staff+ only)",
    "account_type": "savings|checking|business",
    "currency": "USD|EUR|GBP|JPY|CAD|AUD",
    "is_primary": true|false,
    "overdraft_limit": "decimal (staff+ only)",
    "interest_rate": "decimal (manager+ only)",
    "minimum_balance": "decimal"
}
```

**Response**:
```json
{
    "success": true,
    "message": "Account created successfully",
    "data": {
        "id": "uuid",
        "account_number": "************",
        "account_type": "savings",
        "balance": "0.00",
        "currency": "USD",
        "status": "active",
        "is_primary": true,
        "created_at": "2025-01-22T10:30:00Z"
    }
}
```

### 2. Update Account Status

**Endpoint**: `PATCH /api/v1/accounts/{id}/status/`

**Description**: Update account status for administrative purposes.

**Permissions**:
- Staff+: Can activate/deactivate/suspend accounts
- Admin+: Can permanently close accounts

**Request Body**:
```json
{
    "status": "active|inactive|suspended|closed",
    "status_reason": "string (required for suspension/closure)"
}
```

**Response**:
```json
{
    "success": true,
    "message": "Account status updated from active to suspended",
    "data": {
        "id": "uuid",
        "account_number": "************",
        "old_status": "active",
        "new_status": "suspended",
        "updated_by": "<EMAIL>",
        "reason": "Suspicious activity detected"
    }
}
```

### 3. Update Account Balance

**Endpoint**: `PATCH /api/v1/accounts/{id}/balance/`

**Description**: Adjust account balance for administrative operations.

**Permissions**: Admin+ only

**Request Body**:
```json
{
    "adjustment_amount": "250.00",
    "adjustment_reason": "Promotional credit"
}
```

**Response**:
```json
{
    "success": true,
    "message": "Account balance updated successfully",
    "data": {
        "id": "uuid",
        "account_number": "************",
        "old_balance": "1000.00",
        "adjustment_amount": "250.00",
        "new_balance": "1250.00",
        "adjusted_by": "<EMAIL>",
        "reason": "Promotional credit",
        "timestamp": "2025-01-22T10:30:00Z"
    }
}
```

### 4. Close/Deactivate Account

**Endpoint**: `PATCH /api/v1/accounts/{id}/close/`

**Description**: Close or deactivate accounts with proper validation.

**Permissions**: Admin+ only

**Request Body**:
```json
{
    "closure_type": "close|deactivate",
    "status_reason": "Customer requested account closure"
}
```

**Response**:
```json
{
    "success": true,
    "message": "Account closed successfully",
    "data": {
        "id": "uuid",
        "account_number": "************",
        "old_status": "active",
        "new_status": "closed",
        "closure_type": "close",
        "reason": "Customer requested account closure",
        "closed_by": "<EMAIL>",
        "timestamp": "2025-01-22T10:30:00Z"
    }
}
```

## Account Number Generation

The system automatically generates unique 12-digit account numbers with the following format:

- **Positions 1-3**: Bank code (001 for ExoBank)
- **Positions 4-5**: Account type code
  - 10: Savings accounts
  - 20: Checking accounts  
  - 30: Business accounts
- **Positions 6-12**: Unique sequence number

Example: `************` (ExoBank Savings Account)

## Validation Rules

### Account Creation
- Customers cannot create business accounts
- Only staff+ can set overdraft limits
- Only manager+ can set custom interest rates
- Business accounts require manager+ approval
- Primary account validation prevents duplicates

### Status Updates
- Reason required for suspension/closure
- Closed accounts cannot be reopened
- Only admin+ can permanently close accounts

### Balance Adjustments
- Reason required for all adjustments
- Overdraft limits respected
- Maximum adjustment amount: $1,000,000
- Only admin+ can perform adjustments

### Account Closure
- Zero balance required for permanent closure
- Outstanding balance prevents closure
- Reason required for all closures

## Error Responses

All endpoints return consistent error responses:

```json
{
    "success": false,
    "message": "Error description",
    "error": "Detailed error information"
}
```

Common HTTP status codes:
- `400 Bad Request`: Validation errors
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Account not found
- `500 Internal Server Error`: Server error

## Integration with RLS Policies

All endpoints work seamlessly with the Supabase Row Level Security (RLS) policies implemented in Priority 1, ensuring:

- Database-level security enforcement
- Role hierarchy respected at data layer
- Audit trail for all operations
- Compliance with banking regulations

## Testing

Comprehensive test suite covers:
- Role-based permission validation
- Account number generation uniqueness
- Error handling and validation
- RLS policy integration
- Cross-field validation rules

Run tests with:
```bash
python manage.py test apps.accounts.tests -v 2
```
