/**
 * JWT Utilities for ExoBank Frontend Applications
 * 
 * Shared utilities for JWT token handling, expiration checking, and refresh logic
 * that can be used by both React Native and Next.js applications.
 */

/**
 * Decode JWT token payload without verification
 * Note: This is for client-side expiration checking only, not for security validation
 */
export function decodeJWTPayload(token: string): any {
  try {
    // JWT tokens have 3 parts separated by dots: header.payload.signature
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT token format');
    }

    // Decode the payload (second part)
    const payload = parts[1];
    
    // Add padding if needed for base64 decoding
    const paddedPayload = payload + '='.repeat((4 - payload.length % 4) % 4);
    
    // Decode base64
    const decodedPayload = atob(paddedPayload);
    
    // Parse JSON
    return JSON.parse(decodedPayload);
  } catch (error) {
    console.error('Error decoding JWT payload:', error);
    return null;
  }
}

/**
 * Check if JWT token is expired
 */
export function isTokenExpired(token: string): boolean {
  try {
    const payload = decodeJWTPayload(token);
    if (!payload || !payload.exp) {
      return true; // Consider invalid tokens as expired
    }

    // JWT exp is in seconds, Date.now() is in milliseconds
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true; // Consider error as expired
  }
}

/**
 * Check if token should be refreshed based on expiration threshold
 * @param token JWT access token
 * @param thresholdMinutes Minutes before expiration to trigger refresh (default: 5)
 */
export function shouldRefreshToken(token: string, thresholdMinutes: number = 5): boolean {
  try {
    const payload = decodeJWTPayload(token);
    if (!payload || !payload.exp) {
      return true; // Refresh if token is invalid
    }

    // Calculate threshold time (current time + threshold in seconds)
    const currentTime = Math.floor(Date.now() / 1000);
    const thresholdTime = currentTime + (thresholdMinutes * 60);
    
    // Refresh if token expires within threshold
    return payload.exp < thresholdTime;
  } catch (error) {
    console.error('Error checking token refresh need:', error);
    return true; // Refresh on error
  }
}

/**
 * Get token expiration time as timestamp
 */
export function getTokenExpirationTime(token: string): number | null {
  try {
    const payload = decodeJWTPayload(token);
    if (!payload || !payload.exp) {
      return null;
    }

    // Convert from seconds to milliseconds
    return payload.exp * 1000;
  } catch (error) {
    console.error('Error getting token expiration time:', error);
    return null;
  }
}

/**
 * Get user information from JWT token
 */
export function getUserFromToken(token: string): any {
  try {
    const payload = decodeJWTPayload(token);
    if (!payload) {
      return null;
    }

    // Extract user information from token payload
    return {
      id: payload.user_id || payload.sub,
      email: payload.email,
      role: payload.role,
      username: payload.username,
      // Add other fields as needed based on your JWT payload structure
    };
  } catch (error) {
    console.error('Error extracting user from token:', error);
    return null;
  }
}

/**
 * Validate token format (basic check)
 */
export function isValidTokenFormat(token: string): boolean {
  if (!token || typeof token !== 'string') {
    return false;
  }

  // JWT should have 3 parts separated by dots
  const parts = token.split('.');
  return parts.length === 3 && parts.every(part => part.length > 0);
}

/**
 * Get time until token expires (in milliseconds)
 */
export function getTimeUntilExpiration(token: string): number | null {
  try {
    const expirationTime = getTokenExpirationTime(token);
    if (!expirationTime) {
      return null;
    }

    const currentTime = Date.now();
    const timeUntilExpiration = expirationTime - currentTime;
    
    return Math.max(0, timeUntilExpiration); // Return 0 if already expired
  } catch (error) {
    console.error('Error calculating time until expiration:', error);
    return null;
  }
}

/**
 * Format time duration for display
 */
export function formatTokenExpirationTime(token: string): string {
  try {
    const timeUntilExpiration = getTimeUntilExpiration(token);
    if (!timeUntilExpiration) {
      return 'Invalid token';
    }

    if (timeUntilExpiration === 0) {
      return 'Expired';
    }

    const minutes = Math.floor(timeUntilExpiration / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''}`;
    } else if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''}`;
    } else {
      return `${minutes} minute${minutes > 1 ? 's' : ''}`;
    }
  } catch (error) {
    console.error('Error formatting expiration time:', error);
    return 'Unknown';
  }
}

/**
 * JWT Token Management Configuration
 */
export const JWT_CONFIG = {
  // Default refresh threshold (5 minutes before expiration)
  DEFAULT_REFRESH_THRESHOLD_MINUTES: 5,
  
  // Token storage keys
  ACCESS_TOKEN_KEY: 'auth_token',
  REFRESH_TOKEN_KEY: 'refresh_token',
  USER_DATA_KEY: 'user_data',
  
  // Token validation settings
  MIN_TOKEN_LENGTH: 10,
  MAX_TOKEN_AGE_DAYS: 7,
} as const;

/**
 * JWT Error types
 */
export enum JWTError {
  INVALID_FORMAT = 'INVALID_FORMAT',
  EXPIRED = 'EXPIRED',
  DECODE_ERROR = 'DECODE_ERROR',
  MISSING_PAYLOAD = 'MISSING_PAYLOAD',
  REFRESH_NEEDED = 'REFRESH_NEEDED',
}

/**
 * JWT validation result
 */
export interface JWTValidationResult {
  isValid: boolean;
  error?: JWTError;
  payload?: any;
  expiresAt?: number;
  shouldRefresh?: boolean;
}

/**
 * Comprehensive JWT token validation
 */
export function validateJWTToken(token: string, refreshThresholdMinutes?: number): JWTValidationResult {
  try {
    // Check format
    if (!isValidTokenFormat(token)) {
      return {
        isValid: false,
        error: JWTError.INVALID_FORMAT,
      };
    }

    // Decode payload
    const payload = decodeJWTPayload(token);
    if (!payload) {
      return {
        isValid: false,
        error: JWTError.DECODE_ERROR,
      };
    }

    // Check if expired
    if (isTokenExpired(token)) {
      return {
        isValid: false,
        error: JWTError.EXPIRED,
        payload,
      };
    }

    // Check if should refresh
    const shouldRefresh = shouldRefreshToken(token, refreshThresholdMinutes);
    const expiresAt = getTokenExpirationTime(token);

    return {
      isValid: true,
      payload,
      expiresAt: expiresAt || undefined,
      shouldRefresh,
    };
  } catch (error) {
    console.error('Error validating JWT token:', error);
    return {
      isValid: false,
      error: JWTError.DECODE_ERROR,
    };
  }
}
