"""
Serializers for the accounts app.
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from decimal import Decimal
import re
from .models import Account, Beneficiary
from .utils import create_account_with_number, validate_account_number

User = get_user_model()


class AccountSerializer(serializers.ModelSerializer):
    """
    Serializer for Account model with comprehensive validation.
    """
    user_email = serializers.CharField(source='user.email', read_only=True)
    user_name = serializers.CharField(source='user.full_name', read_only=True)
    balance_display = serializers.SerializerMethodField()
    
    class Meta:
        model = Account
        fields = [
            'id', 'user', 'user_email', 'user_name', 'account_number',
            'account_type', 'balance', 'balance_display', 'currency', 'status',
            'is_primary', 'overdraft_limit', 'interest_rate', 'minimum_balance',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'user', 'account_number', 'created_at', 'updated_at']
    
    def get_balance_display(self, obj):
        """Format balance for display with currency symbol."""
        currency_symbols = {
            'USD': '$',
            'EUR': '€',
            'GBP': '£',
            'JPY': '¥'
        }
        symbol = currency_symbols.get(obj.currency, obj.currency)
        return f"{symbol}{obj.balance:,.2f}"
    
    def validate_balance(self, value):
        """Validate account balance."""
        if value < Decimal('0.00'):
            raise serializers.ValidationError("Account balance cannot be negative.")
        
        if value > Decimal('************.99'):
            raise serializers.ValidationError("Account balance exceeds maximum allowed amount.")
        
        return value
    
    def validate_currency(self, value):
        """Validate currency code."""
        valid_currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD']
        if value not in valid_currencies:
            raise serializers.ValidationError(
                f"Invalid currency code. Supported currencies: {', '.join(valid_currencies)}"
            )
        return value
    
    def validate(self, attrs):
        """Perform cross-field validation."""
        user = self.context['request'].user
        
        # Only allow customers to create savings and checking accounts
        if user.role == User.Role.CUSTOMER:
            account_type = attrs.get('account_type', self.instance.account_type if self.instance else None)
            if account_type == Account.AccountType.BUSINESS:
                raise serializers.ValidationError({
                    'account_type': 'Customers cannot create business accounts.'
                })
        
        return attrs


class BeneficiarySerializer(serializers.ModelSerializer):
    """
    Serializer for Beneficiary model with comprehensive validation.
    """
    
    class Meta:
        model = Beneficiary
        fields = [
            'id', 'user', 'name', 'account_number', 'bank_name',
            'bank_code', 'routing_number', 'swift_code', 'beneficiary_type',
            'relationship', 'email', 'phone_number', 'address',
            'is_verified', 'verification_date', 'status', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'user', 'is_verified', 'verification_date', 'created_at', 'updated_at']
    
    def validate_name(self, value):
        """Validate beneficiary name."""
        if not value or len(value.strip()) < 2:
            raise serializers.ValidationError("Beneficiary name must be at least 2 characters long.")
        
        if len(value) > 100:
            raise serializers.ValidationError("Beneficiary name cannot exceed 100 characters.")
        
        # Check for valid characters (letters, spaces, hyphens, apostrophes)
        if not re.match(r"^[a-zA-Z\s\-'\.]+$", value):
            raise serializers.ValidationError(
                "Beneficiary name can only contain letters, spaces, hyphens, apostrophes, and periods."
            )
        
        return value.strip()
    
    def validate_account_number(self, value):
        """Validate account number format."""
        if not value or len(value.strip()) < 8:
            raise serializers.ValidationError("Account number must be at least 8 characters long.")
        
        if len(value) > 20:
            raise serializers.ValidationError("Account number cannot exceed 20 characters.")
        
        # Check for valid account number format (alphanumeric)
        if not re.match(r"^[a-zA-Z0-9]+$", value):
            raise serializers.ValidationError(
                "Account number can only contain letters and numbers."
            )
        
        return value.strip().upper()
    
    def validate_bank_name(self, value):
        """Validate bank name."""
        if not value or len(value.strip()) < 2:
            raise serializers.ValidationError("Bank name must be at least 2 characters long.")
        
        if len(value) > 100:
            raise serializers.ValidationError("Bank name cannot exceed 100 characters.")
        
        # Check for valid characters
        if not re.match(r"^[a-zA-Z0-9\s\-'\.&]+$", value):
            raise serializers.ValidationError(
                "Bank name can only contain letters, numbers, spaces, hyphens, apostrophes, periods, and ampersands."
            )
        
        return value.strip()
    
    def validate_routing_number(self, value):
        """Validate routing number format."""
        if value:  # Optional field
            if not re.match(r"^[0-9]{9}$", value):
                raise serializers.ValidationError(
                    "Routing number must be exactly 9 digits."
                )
        
        return value
    
    def validate(self, attrs):
        """Perform cross-field validation."""
        user = self.context['request'].user
        account_number = attrs.get('account_number')
        
        # Check for duplicate beneficiary account numbers for the same user
        if account_number:
            existing_beneficiary = Beneficiary.objects.filter(
                user=user,
                account_number=account_number,
                is_active=True
            ).exclude(id=self.instance.id if self.instance else None)
            
            if existing_beneficiary.exists():
                raise serializers.ValidationError({
                    'account_number': 'You already have a beneficiary with this account number.'
                })
        
        # Prevent users from adding their own accounts as beneficiaries
        if user.role == User.Role.CUSTOMER:
            user_accounts = user.accounts.values_list('account_number', flat=True)
            if account_number in user_accounts:
                raise serializers.ValidationError({
                    'account_number': 'You cannot add your own account as a beneficiary.'
                })
        
        return attrs


class AccountCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating new accounts with role-based validation.
    """
    user_id = serializers.UUIDField(write_only=True, required=False)

    class Meta:
        model = Account
        fields = [
            'user_id', 'account_type', 'currency', 'is_primary',
            'overdraft_limit', 'interest_rate', 'minimum_balance'
        ]

    def validate_user_id(self, value):
        """Validate user_id for staff+ roles creating accounts for others."""
        request_user = self.context['request'].user

        # If user_id is provided, only staff+ can create accounts for others
        if value and not request_user.is_staff_member:
            raise serializers.ValidationError(
                "Only staff members can create accounts for other users."
            )

        # Validate that the target user exists and is active
        if value:
            try:
                target_user = User.objects.get(id=value, status=User.Status.ACTIVE)

                # Check role hierarchy - can't create accounts for users of equal or higher role
                if not request_user.can_manage_user(target_user):
                    raise serializers.ValidationError(
                        "You don't have permission to create accounts for this user."
                    )

            except User.DoesNotExist:
                raise serializers.ValidationError("Invalid user ID or user is not active.")

        return value

    def validate_account_type(self, value):
        """Validate account type based on user role."""
        request_user = self.context['request'].user

        # Customers can only create savings and checking accounts
        if request_user.role == User.Role.CUSTOMER and value == Account.AccountType.BUSINESS:
            raise serializers.ValidationError(
                "Customers cannot create business accounts. Please contact support."
            )

        return value

    def validate_overdraft_limit(self, value):
        """Validate overdraft limit based on user role."""
        request_user = self.context['request'].user

        # Only staff+ can set overdraft limits
        if value and value > Decimal('0.00') and not request_user.is_staff_member:
            raise serializers.ValidationError(
                "Only staff members can set overdraft limits."
            )

        if value and value > Decimal('50000.00'):
            raise serializers.ValidationError(
                "Overdraft limit cannot exceed $50,000."
            )

        return value

    def validate_interest_rate(self, value):
        """Validate interest rate based on user role."""
        request_user = self.context['request'].user

        # Only manager+ can set custom interest rates
        if value and value > Decimal('0.0000') and not request_user.is_manager_or_above:
            raise serializers.ValidationError(
                "Only managers and above can set custom interest rates."
            )

        if value and value > Decimal('0.1000'):  # 10% max
            raise serializers.ValidationError(
                "Interest rate cannot exceed 10%."
            )

        return value

    def validate(self, attrs):
        """Perform cross-field validation."""
        request_user = self.context['request'].user
        user_id = attrs.get('user_id')

        # Determine the target user
        target_user = request_user
        if user_id:
            target_user = User.objects.get(id=user_id)

        # Check if user already has a primary account of this type
        account_type = attrs.get('account_type')
        is_primary = attrs.get('is_primary', False)

        if is_primary:
            existing_primary = Account.objects.filter(
                user=target_user,
                account_type=account_type,
                is_primary=True,
                status__in=[Account.Status.ACTIVE, Account.Status.INACTIVE]
            ).exists()

            if existing_primary:
                raise serializers.ValidationError({
                    'is_primary': f'User already has a primary {account_type} account.'
                })

        # Business accounts require manager+ approval
        if account_type == Account.AccountType.BUSINESS and not request_user.is_manager_or_above:
            raise serializers.ValidationError({
                'account_type': 'Business accounts require manager approval. Please contact your manager.'
            })

        return attrs

    def create(self, validated_data):
        """Create account with auto-generated account number."""
        request_user = self.context['request'].user
        user_id = validated_data.pop('user_id', None)

        # Determine the target user
        target_user = request_user
        if user_id:
            target_user = User.objects.get(id=user_id)

        # Create account with auto-generated account number
        account = create_account_with_number(
            user=target_user,
            **validated_data
        )

        return account


class AccountStatusSerializer(serializers.ModelSerializer):
    """
    Serializer for updating account status (staff+ only).
    """
    status_reason = serializers.CharField(
        write_only=True,
        required=False,
        max_length=500,
        help_text="Reason for status change (required for suspension/closure)"
    )

    class Meta:
        model = Account
        fields = ['status', 'status_reason']

    def validate_status(self, value):
        """Validate status change permissions."""
        request_user = self.context['request'].user

        # Only staff+ can change account status
        if not request_user.is_staff_member:
            raise serializers.ValidationError(
                "Only staff members can change account status."
            )

        # Only admin+ can permanently close accounts
        if value == Account.Status.CLOSED and not request_user.is_admin_or_above:
            raise serializers.ValidationError(
                "Only administrators can permanently close accounts."
            )

        return value

    def validate(self, attrs):
        """Validate status change requirements."""
        status = attrs.get('status')
        status_reason = attrs.get('status_reason')
        current_status = self.instance.status if self.instance else None

        # Require reason for suspension or closure
        if status in [Account.Status.SUSPENDED, Account.Status.CLOSED]:
            if not status_reason:
                raise serializers.ValidationError({
                    'status_reason': f'Reason is required when changing status to {status}.'
                })

        # Prevent reopening closed accounts
        if current_status == Account.Status.CLOSED and status != Account.Status.CLOSED:
            raise serializers.ValidationError({
                'status': 'Closed accounts cannot be reopened. Please create a new account.'
            })

        return attrs


class AccountBalanceSerializer(serializers.ModelSerializer):
    """
    Serializer for updating account balance (admin operations only).
    """
    adjustment_amount = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
        write_only=True,
        help_text="Amount to add (positive) or subtract (negative) from current balance"
    )
    adjustment_reason = serializers.CharField(
        write_only=True,
        required=True,
        max_length=500,
        help_text="Reason for balance adjustment (required for audit trail)"
    )

    class Meta:
        model = Account
        fields = ['balance', 'adjustment_amount', 'adjustment_reason']
        read_only_fields = ['balance']

    def validate_adjustment_amount(self, value):
        """Validate adjustment amount."""
        if value == Decimal('0.00'):
            raise serializers.ValidationError(
                "Adjustment amount cannot be zero."
            )

        if abs(value) > Decimal('1000000.00'):
            raise serializers.ValidationError(
                "Adjustment amount cannot exceed $1,000,000."
            )

        return value

    def validate(self, attrs):
        """Validate balance adjustment permissions and limits."""
        request_user = self.context['request'].user

        # Only admin+ can adjust balances
        if not request_user.is_admin_or_above:
            raise serializers.ValidationError(
                "Only administrators can adjust account balances."
            )

        adjustment_amount = attrs.get('adjustment_amount')
        current_balance = self.instance.balance if self.instance else Decimal('0.00')
        new_balance = current_balance + adjustment_amount

        # Prevent negative balances unless overdraft is available
        if new_balance < Decimal('0.00'):
            overdraft_limit = self.instance.overdraft_limit if self.instance else Decimal('0.00')
            if abs(new_balance) > overdraft_limit:
                raise serializers.ValidationError({
                    'adjustment_amount': f'Adjustment would exceed overdraft limit. '
                                       f'Current balance: ${current_balance}, '
                                       f'Overdraft limit: ${overdraft_limit}'
                })

        # Prevent balances exceeding maximum
        if new_balance > Decimal('************.99'):
            raise serializers.ValidationError({
                'adjustment_amount': 'Adjustment would exceed maximum account balance.'
            })

        return attrs

    def update(self, instance, validated_data):
        """Update balance with adjustment amount."""
        adjustment_amount = validated_data.pop('adjustment_amount')
        adjustment_reason = validated_data.pop('adjustment_reason')

        # Calculate new balance
        new_balance = instance.balance + adjustment_amount
        instance.balance = new_balance
        instance.save()

        # TODO: Create audit log entry for balance adjustment
        # This would typically be handled by a separate audit logging system

        return instance
