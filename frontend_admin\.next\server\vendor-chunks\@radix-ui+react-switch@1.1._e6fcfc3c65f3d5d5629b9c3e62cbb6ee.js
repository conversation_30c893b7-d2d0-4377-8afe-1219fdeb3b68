"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-switch@1.1._e6fcfc3c65f3d5d5629b9c3e62cbb6ee";
exports.ids = ["vendor-chunks/@radix-ui+react-switch@1.1._e6fcfc3c65f3d5d5629b9c3e62cbb6ee"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-switch@1.1._e6fcfc3c65f3d5d5629b9c3e62cbb6ee/node_modules/@radix-ui/react-switch/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-switch@1.1._e6fcfc3c65f3d5d5629b9c3e62cbb6ee/node_modules/@radix-ui/react-switch/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Switch: () => (/* binding */ Switch),\n/* harmony export */   SwitchThumb: () => (/* binding */ SwitchThumb),\n/* harmony export */   Thumb: () => (/* binding */ Thumb),\n/* harmony export */   createSwitchScope: () => (/* binding */ createSwitchScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_9db7fd351a7a6a77f86bf86a6ddecee8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_2a0e526a8f7e7aada080206d385bb572/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_4fe40d510edca7ae4ca9c92afeb1ae6d/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_84b05e8d8acde331bf79519153011e46/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-previou_f62eb26ebf07111fa37167228db23a06/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-size@1._25ea7b66547079fee23d7c838aabe8ed/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_997b35f2e2aa9d3174fc03a0f79e437b/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_9db7fd351a7a6a77f86bf86a6ddecee8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Root,Switch,SwitchThumb,Thumb,createSwitchScope auto */ // packages/react/switch/src/Switch.tsx\n\n\n\n\n\n\n\n\n\nvar SWITCH_NAME = \"Switch\";\nvar [createSwitchContext, createSwitchScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(SWITCH_NAME);\nvar [SwitchProvider, useSwitchContext] = createSwitchContext(SWITCH_NAME);\nvar Switch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSwitch, name, checked: checkedProp, defaultChecked, required, disabled, value = \"on\", onCheckedChange, form, ...switchProps } = props;\n    const [button, setButton] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"Switch.useComposedRefs[composedRefs]\": (node)=>setButton(node)\n    }[\"Switch.useComposedRefs[composedRefs]\"]);\n    const hasConsumerStoppedPropagationRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isFormControl = button ? form || !!button.closest(\"form\") : true;\n    const [checked = false, setChecked] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: checkedProp,\n        defaultProp: defaultChecked,\n        onChange: onCheckedChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(SwitchProvider, {\n        scope: __scopeSwitch,\n        checked,\n        disabled,\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.button, {\n                type: \"button\",\n                role: \"switch\",\n                \"aria-checked\": checked,\n                \"aria-required\": required,\n                \"data-state\": getState(checked),\n                \"data-disabled\": disabled ? \"\" : void 0,\n                disabled,\n                value,\n                ...switchProps,\n                ref: composedRefs,\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onClick, (event)=>{\n                    setChecked((prevChecked)=>!prevChecked);\n                    if (isFormControl) {\n                        hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n                        if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n                    }\n                })\n            }),\n            isFormControl && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(BubbleInput, {\n                control: button,\n                bubbles: !hasConsumerStoppedPropagationRef.current,\n                name,\n                value,\n                checked,\n                required,\n                disabled,\n                form,\n                style: {\n                    transform: \"translateX(-100%)\"\n                }\n            })\n        ]\n    });\n});\nSwitch.displayName = SWITCH_NAME;\nvar THUMB_NAME = \"SwitchThumb\";\nvar SwitchThumb = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSwitch, ...thumbProps } = props;\n    const context = useSwitchContext(THUMB_NAME, __scopeSwitch);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.span, {\n        \"data-state\": getState(context.checked),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        ...thumbProps,\n        ref: forwardedRef\n    });\n});\nSwitchThumb.displayName = THUMB_NAME;\nvar BubbleInput = (props)=>{\n    const { control, checked, bubbles = true, ...inputProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevChecked = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_7__.usePrevious)(checked);\n    const controlSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_8__.useSize)(control);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"BubbleInput.useEffect\": ()=>{\n            const input = ref.current;\n            const inputProto = window.HTMLInputElement.prototype;\n            const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"checked\");\n            const setChecked = descriptor.set;\n            if (prevChecked !== checked && setChecked) {\n                const event = new Event(\"click\", {\n                    bubbles\n                });\n                setChecked.call(input, checked);\n                input.dispatchEvent(event);\n            }\n        }\n    }[\"BubbleInput.useEffect\"], [\n        prevChecked,\n        checked,\n        bubbles\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"input\", {\n        type: \"checkbox\",\n        \"aria-hidden\": true,\n        defaultChecked: checked,\n        ...inputProps,\n        tabIndex: -1,\n        ref,\n        style: {\n            ...props.style,\n            ...controlSize,\n            position: \"absolute\",\n            pointerEvents: \"none\",\n            opacity: 0,\n            margin: 0\n        }\n    });\n};\nfunction getState(checked) {\n    return checked ? \"checked\" : \"unchecked\";\n}\nvar Root = Switch;\nvar Thumb = SwitchThumb;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-switch@1.1._e6fcfc3c65f3d5d5629b9c3e62cbb6ee/node_modules/@radix-ui/react-switch/dist/index.mjs\n");

/***/ })

};
;