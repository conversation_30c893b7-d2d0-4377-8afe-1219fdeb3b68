/**
 * TypeScript interfaces matching backend admin API responses
 * 
 * These interfaces define the structure of data returned from the Django backend
 * admin API endpoints, ensuring type safety throughout the admin dashboard.
 */

// Base API Response Structure
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: Record<string, string[]>
    timestamp?: string
    path?: string
  }
  message?: string
}

// Paginated Response Structure
export interface PaginatedResponse<T> {
  count: number
  next: string | null
  previous: string | null
  results: T[]
}

// User Related Types
export interface User {
  id: string
  email: string
  username: string
  first_name: string
  last_name: string
  is_staff: boolean
  is_superuser: boolean
  is_active: boolean
  date_joined: string
  last_login?: string
  role: 'customer' | 'staff' | 'manager' | 'admin' | 'super_admin'
  status: 'active' | 'inactive' | 'suspended' | 'pending'
  is_kyc_verified?: boolean
  phone_number?: string
  date_of_birth?: string
  address?: string
  last_login_ip?: string
  created_at: string
  updated_at: string
}

export interface AdminUser extends User {
  permissions: string[]
  accounts_count?: number
  transactions_count?: number
  last_activity?: string
}

// Authentication Types
export interface LoginRequest {
  email: string
  password: string
}

export interface LoginResponse {
  access: string
  refresh: string
  user: User
}

export interface AuthData {
  access: string | null
  refresh: string | null
  user: User | null
}

// Dashboard Statistics Types
export interface DashboardStats {
  total_users: number
  active_users: number
  pending_users: number
  suspended_users: number
  total_accounts: number
  total_balance: number
  total_transactions: number
  pending_transactions: number
  failed_transactions: number
  security_alerts: number
  recent_logins: number
  failed_logins_today: number
  new_users_today?: number
}

// Audit Log Types
export interface AuditEvent {
  id: string
  user_id: string | null
  user_email: string | null
  action: string
  action_display: string
  level: 'debug' | 'info' | 'warning' | 'error' | 'critical'
  level_display: string
  description: string
  ip_address: string | null
  user_agent: string | null
  created_at: string
  metadata?: Record<string, any>
}

// Security Event Types
export interface SecurityEvent {
  id: string
  user_id: string | null
  user_email: string | null
  event_type: string
  event_type_display: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  severity_display: string
  status: 'open' | 'investigating' | 'resolved' | 'false_positive'
  status_display: string
  description: string
  ip_address: string | null
  user_agent: string | null
  created_at: string
  updated_at: string
  resolved_at?: string
  resolved_by?: string
  resolution_notes?: string
  metadata?: Record<string, any>
}

// Login Attempt Types
export interface LoginAttempt {
  id: string
  user_id: string | null
  user_email: string | null
  email: string
  status: 'success' | 'failed' | 'blocked'
  status_display: string
  ip_address: string
  user_agent: string
  failure_reason?: string
  created_at: string
  metadata?: Record<string, any>
}

// Account Types
export interface Account {
  id: string
  user: string
  user_email: string
  user_name: string
  account_number: string
  account_type: 'savings' | 'checking' | 'business'
  account_type_display: string
  balance: string
  balance_display: string
  currency: string
  status: 'active' | 'inactive' | 'suspended' | 'closed'
  status_display: string
  created_at: string
  updated_at: string
}

// Account Request Types
export interface AccountCreateRequest {
  account_type: 'savings' | 'checking' | 'business'
  initial_balance?: string | number
  user_id?: string
  user_email?: string
  user_first_name?: string
  user_last_name?: string
  user_phone?: string
  user_address?: string
  notes?: string
}

export interface AccountUpdateRequest {
  account_type?: 'savings' | 'checking' | 'business'
  balance?: string | number
  status?: 'active' | 'inactive' | 'suspended' | 'closed'
}

export interface AccountStatusUpdateRequest {
  status: 'active' | 'inactive' | 'suspended' | 'closed'
  reason?: string
}

export interface AccountBalanceUpdateRequest {
  balance: string | number
  reason?: string
}

// Transaction Types
export interface Transaction {
  id: string
  user: string
  user_email: string
  from_account: string | null
  from_account_number: string | null
  from_account_type: string | null
  to_account: string | null
  to_account_number: string | null
  to_account_type: string | null
  transaction_type: 'deposit' | 'withdrawal' | 'transfer' | 'payment'
  transaction_type_display: string
  amount: string
  amount_display: string
  currency: string
  description: string
  reference_number: string
  status: 'pending' | 'completed' | 'failed' | 'cancelled'
  status_display: string
  created_at: string
  updated_at: string
  processed_at?: string
}

// Beneficiary Types
export interface Beneficiary {
  id: string
  user: string
  name: string
  account_number: string
  bank_name: string
  routing_number?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

// User Management Types
export interface UserCreateRequest {
  email: string
  password: string
  first_name: string
  last_name: string
  role: User['role']
  phone_number?: string
  date_of_birth?: string
  address?: string
}

export interface UserUpdateRequest {
  first_name?: string
  last_name?: string
  role?: User['role']
  status?: User['status']
  is_kyc_verified?: boolean
  phone_number?: string
  date_of_birth?: string
  address?: string
}

export interface UserDeactivateRequest {
  status: 'inactive' | 'suspended'
  deactivation_reason?: string
}

// Security Alert Update Types
export interface SecurityEventUpdateRequest {
  status?: SecurityEvent['status']
  resolution_notes?: string
}

// API Request Options
export interface ApiRequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'
  headers?: Record<string, string>
  body?: any
  timeout?: number
  retries?: number
}

// Filter and Search Types
export interface UserFilters {
  role?: User['role']
  status?: User['status']
  search?: string
  created_after?: string
  created_before?: string
  ordering?: string
  page?: number
  page_size?: number
}

export interface AuditLogFilters {
  action?: string
  level?: AuditEvent['level']
  user?: string
  date_from?: string
  date_to?: string
  search?: string
  ordering?: string
  page?: number
  page_size?: number
}

export interface SecurityEventFilters {
  event_type?: string
  severity?: SecurityEvent['severity']
  status?: SecurityEvent['status']
  user?: string
  search?: string
  ordering?: string
  page?: number
  page_size?: number
}

export interface LoginAttemptFilters {
  status?: LoginAttempt['status']
  email?: string
  ip_address?: string
  date_from?: string
  date_to?: string
  search?: string
  ordering?: string
  page?: number
  page_size?: number
}

// Error Types
export interface ApiError extends Error {
  code?: string
  status?: number
  details?: Record<string, string[]>
  timestamp?: string
  path?: string
}

// Network Service Types
export interface NetworkConfig {
  baseURL: string
  timeout: number
  retries: number
  retryDelay: number
}

export interface RequestInterceptor {
  onRequest?: (config: any) => any
  onRequestError?: (error: any) => any
}

export interface ResponseInterceptor {
  onResponse?: (response: any) => any
  onResponseError?: (error: any) => any
}

// Health Check Types
export interface HealthCheckResponse {
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: string
  version: string
  database: {
    status: 'connected' | 'disconnected'
    response_time?: number
  }
  cache?: {
    status: 'connected' | 'disconnected'
    response_time?: number
  }
  external_services?: Record<string, {
    status: 'available' | 'unavailable'
    response_time?: number
  }>
}