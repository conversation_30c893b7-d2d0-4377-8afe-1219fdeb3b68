"""
Tests for accounts app API endpoints.
"""

from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from django.urls import reverse
from decimal import Decimal
from .models import Account, Beneficiary
from .utils import generate_account_number, validate_account_number

User = get_user_model()


class AccountAPITestCase(APITestCase):
    """Test cases for Account API endpoints."""
    
    def setUp(self):
        """Set up test data."""
        # Create test users
        self.customer_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='<PERSON>',
            last_name='<PERSON><PERSON>',
            role=User.Role.CUSTOMER,
            status=User.Status.ACTIVE
        )
        
        self.staff_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Jane',
            last_name='Staff',
            role=User.Role.STAFF,
            status=User.Status.ACTIVE
        )
        
        # Create test accounts
        self.customer_account = Account.objects.create(
            user=self.customer_user,
            account_number='ACC*********',
            account_type=Account.AccountType.SAVINGS,
            balance=Decimal('1000.00'),
            currency='USD',
            status=Account.Status.ACTIVE
        )
        
        self.other_account = Account.objects.create(
            user=self.staff_user,
            account_number='ACC*********',
            account_type=Account.AccountType.CHECKING,
            balance=Decimal('2000.00'),
            currency='USD',
            status=Account.Status.ACTIVE
        )
    
    def test_account_list_customer_access(self):
        """Test that customers can only see their own accounts."""
        self.client.force_authenticate(user=self.customer_user)
        url = reverse('accounts:account_list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(len(response.data['data']), 1)
        self.assertEqual(response.data['data'][0]['id'], str(self.customer_account.id))
    
    def test_account_list_staff_access(self):
        """Test that staff can see all accounts."""
        self.client.force_authenticate(user=self.staff_user)
        url = reverse('accounts:account_list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(len(response.data['data']), 2)
    
    def test_account_list_filtering(self):
        """Test account list filtering functionality."""
        self.client.force_authenticate(user=self.staff_user)
        url = reverse('accounts:account_list')
        
        # Filter by account type
        response = self.client.get(url, {'account_type': 'savings'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(len(response.data['data']), 1)
        
        # Filter by status
        response = self.client.get(url, {'status': 'active'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(len(response.data['data']), 2)
    
    def test_account_detail_access(self):
        """Test account detail access permissions."""
        # Customer accessing their own account
        self.client.force_authenticate(user=self.customer_user)
        url = reverse('accounts:account_detail', kwargs={'pk': self.customer_account.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['account_number'], 'ACC*********')
        
        # Customer trying to access another account
        url = reverse('accounts:account_detail', kwargs={'pk': self.other_account.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_unauthenticated_access(self):
        """Test that unauthenticated users cannot access accounts."""
        url = reverse('accounts:account_list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class BeneficiaryAPITestCase(APITestCase):
    """Test cases for Beneficiary API endpoints."""
    
    def setUp(self):
        """Set up test data."""
        self.customer_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='John',
            last_name='Doe',
            role=User.Role.CUSTOMER,
            status=User.Status.ACTIVE
        )
        
        self.beneficiary = Beneficiary.objects.create(
            user=self.customer_user,
            name='Jane Smith',
            account_number='BEN*********',
            bank_name='Test Bank',
            routing_number='*********',
            is_active=True
        )
    
    def test_beneficiary_list_create(self):
        """Test beneficiary list and create functionality."""
        self.client.force_authenticate(user=self.customer_user)
        url = reverse('accounts:beneficiary_list_create')
        
        # Test list
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(len(response.data['data']), 1)
        
        # Test create
        data = {
            'name': 'Bob Johnson',
            'account_number': 'BEN*********',
            'bank_name': 'Another Bank',
            'routing_number': '*********'
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['name'], 'Bob Johnson')
    
    def test_beneficiary_validation(self):
        """Test beneficiary data validation."""
        self.client.force_authenticate(user=self.customer_user)
        url = reverse('accounts:beneficiary_list_create')
        
        # Test invalid name
        data = {
            'name': 'A',  # Too short
            'account_number': 'BEN*********',
            'bank_name': 'Test Bank'
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Test invalid account number
        data = {
            'name': 'Valid Name',
            'account_number': '123',  # Too short
            'bank_name': 'Test Bank'
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Test duplicate account number
        data = {
            'name': 'Another Name',
            'account_number': 'BEN*********',  # Already exists
            'bank_name': 'Test Bank'
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_beneficiary_detail_operations(self):
        """Test beneficiary detail view operations."""
        self.client.force_authenticate(user=self.customer_user)
        url = reverse('accounts:beneficiary_detail', kwargs={'pk': self.beneficiary.id})
        
        # Test retrieve
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['name'], 'Jane Smith')
        
        # Test update
        data = {'name': 'Jane Updated'}
        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['name'], 'Jane Updated')
        
        # Test soft delete
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # Verify soft delete
        self.beneficiary.refresh_from_db()
        self.assertFalse(self.beneficiary.is_active)


class AccountUtilsTestCase(APITestCase):
    """Test cases for account utility functions."""

    def test_generate_account_number(self):
        """Test account number generation."""
        account_number = generate_account_number()

        # Check format
        self.assertEqual(len(account_number), 12)
        self.assertTrue(account_number.isdigit())
        self.assertTrue(account_number.startswith('001'))

        # Check uniqueness
        account_number2 = generate_account_number()
        self.assertNotEqual(account_number, account_number2)

    def test_validate_account_number(self):
        """Test account number validation."""
        # Valid account number
        self.assertTrue(validate_account_number('************'))

        # Invalid length
        with self.assertRaises(Exception):
            validate_account_number('12345')

        # Invalid bank code
        with self.assertRaises(Exception):
            validate_account_number('************')

        # Invalid type code
        with self.assertRaises(Exception):
            validate_account_number('************')


class AccountCreateAPITestCase(APITestCase):
    """Test cases for Account Creation API."""

    def setUp(self):
        """Set up test data."""
        self.customer_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='John',
            last_name='Doe',
            role=User.Role.CUSTOMER,
            status=User.Status.ACTIVE
        )

        self.staff_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Jane',
            last_name='Staff',
            role=User.Role.STAFF,
            status=User.Status.ACTIVE
        )

        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Admin',
            last_name='User',
            role=User.Role.ADMIN,
            status=User.Status.ACTIVE
        )

        self.manager_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Manager',
            last_name='User',
            role=User.Role.MANAGER,
            status=User.Status.ACTIVE
        )

    def test_customer_create_savings_account(self):
        """Test customer creating a savings account."""
        self.client.force_authenticate(user=self.customer_user)
        url = reverse('accounts:account_create')

        data = {
            'account_type': 'savings',
            'currency': 'USD',
            'is_primary': True
        }

        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['account_type'], 'savings')
        self.assertTrue(response.data['data']['is_primary'])

        # Verify account was created in database
        account = Account.objects.get(id=response.data['data']['id'])
        self.assertEqual(account.user, self.customer_user)
        self.assertEqual(len(account.account_number), 12)

    def test_customer_cannot_create_business_account(self):
        """Test that customers cannot create business accounts."""
        self.client.force_authenticate(user=self.customer_user)
        url = reverse('accounts:account_create')

        data = {
            'account_type': 'business',
            'currency': 'USD'
        }

        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('business', str(response.data))

    def test_staff_create_account_for_customer(self):
        """Test staff creating account for another user."""
        self.client.force_authenticate(user=self.staff_user)
        url = reverse('accounts:account_create')

        data = {
            'user_id': str(self.customer_user.id),
            'account_type': 'checking',
            'currency': 'USD'
        }

        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])

        # Verify account belongs to customer
        account = Account.objects.get(id=response.data['data']['id'])
        self.assertEqual(account.user, self.customer_user)

    def test_customer_cannot_set_overdraft_limit(self):
        """Test that customers cannot set overdraft limits."""
        self.client.force_authenticate(user=self.customer_user)
        url = reverse('accounts:account_create')

        data = {
            'account_type': 'checking',
            'currency': 'USD',
            'overdraft_limit': '1000.00'
        }

        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('overdraft', str(response.data))

    def test_manager_create_business_account(self):
        """Test manager creating business account."""
        self.client.force_authenticate(user=self.manager_user)
        url = reverse('accounts:account_create')

        data = {
            'user_id': str(self.customer_user.id),
            'account_type': 'business',
            'currency': 'USD',
            'interest_rate': '0.0250'
        }

        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['account_type'], 'business')


class AccountStatusUpdateAPITestCase(APITestCase):
    """Test cases for Account Status Update API."""

    def setUp(self):
        """Set up test data."""
        self.customer_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.Role.CUSTOMER,
            status=User.Status.ACTIVE
        )

        self.staff_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.Role.STAFF,
            status=User.Status.ACTIVE
        )

        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.Role.ADMIN,
            status=User.Status.ACTIVE
        )

        self.test_account = Account.objects.create(
            user=self.customer_user,
            account_number='************',
            account_type=Account.AccountType.SAVINGS,
            balance=Decimal('1000.00'),
            status=Account.Status.ACTIVE
        )

    def test_staff_can_suspend_account(self):
        """Test staff can suspend customer accounts."""
        self.client.force_authenticate(user=self.staff_user)
        url = reverse('accounts:account_status_update', kwargs={'pk': self.test_account.id})

        data = {
            'status': 'suspended',
            'status_reason': 'Suspicious activity detected'
        }

        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])

        # Verify status change
        self.test_account.refresh_from_db()
        self.assertEqual(self.test_account.status, Account.Status.SUSPENDED)

    def test_staff_cannot_close_account(self):
        """Test staff cannot permanently close accounts."""
        self.client.force_authenticate(user=self.staff_user)
        url = reverse('accounts:account_status_update', kwargs={'pk': self.test_account.id})

        data = {
            'status': 'closed',
            'status_reason': 'Account closure requested'
        }

        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('administrator', str(response.data))

    def test_admin_can_close_account(self):
        """Test admin can permanently close accounts."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('accounts:account_status_update', kwargs={'pk': self.test_account.id})

        data = {
            'status': 'closed',
            'status_reason': 'Account closure requested by customer'
        }

        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])

        # Verify status change
        self.test_account.refresh_from_db()
        self.assertEqual(self.test_account.status, Account.Status.CLOSED)

    def test_customer_cannot_update_status(self):
        """Test customers cannot update account status."""
        self.client.force_authenticate(user=self.customer_user)
        url = reverse('accounts:account_status_update', kwargs={'pk': self.test_account.id})

        data = {
            'status': 'inactive',
            'status_reason': 'Test reason'
        }

        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_status_reason_required_for_suspension(self):
        """Test that reason is required for suspension."""
        self.client.force_authenticate(user=self.staff_user)
        url = reverse('accounts:account_status_update', kwargs={'pk': self.test_account.id})

        data = {
            'status': 'suspended'
            # Missing status_reason
        }

        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('reason', str(response.data))


class AccountBalanceUpdateAPITestCase(APITestCase):
    """Test cases for Account Balance Update API."""

    def setUp(self):
        """Set up test data."""
        self.customer_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.Role.CUSTOMER,
            status=User.Status.ACTIVE
        )

        self.staff_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.Role.STAFF,
            status=User.Status.ACTIVE
        )

        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.Role.ADMIN,
            status=User.Status.ACTIVE
        )

        self.test_account = Account.objects.create(
            user=self.customer_user,
            account_number='************',
            account_type=Account.AccountType.SAVINGS,
            balance=Decimal('1000.00'),
            overdraft_limit=Decimal('500.00'),
            status=Account.Status.ACTIVE
        )

    def test_admin_can_adjust_balance(self):
        """Test admin can adjust account balance."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('accounts:account_balance_update', kwargs={'pk': self.test_account.id})

        data = {
            'adjustment_amount': '250.00',
            'adjustment_reason': 'Promotional credit'
        }

        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])

        # Verify balance change
        self.test_account.refresh_from_db()
        self.assertEqual(self.test_account.balance, Decimal('1250.00'))

    def test_admin_can_deduct_balance(self):
        """Test admin can deduct from account balance."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('accounts:account_balance_update', kwargs={'pk': self.test_account.id})

        data = {
            'adjustment_amount': '-200.00',
            'adjustment_reason': 'Fee correction'
        }

        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])

        # Verify balance change
        self.test_account.refresh_from_db()
        self.assertEqual(self.test_account.balance, Decimal('800.00'))

    def test_staff_cannot_adjust_balance(self):
        """Test staff cannot adjust account balance."""
        self.client.force_authenticate(user=self.staff_user)
        url = reverse('accounts:account_balance_update', kwargs={'pk': self.test_account.id})

        data = {
            'adjustment_amount': '100.00',
            'adjustment_reason': 'Test adjustment'
        }

        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_overdraft_limit_respected(self):
        """Test that overdraft limit is respected."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('accounts:account_balance_update', kwargs={'pk': self.test_account.id})

        # Try to deduct more than balance + overdraft
        data = {
            'adjustment_amount': '-1600.00',  # Would result in -600, exceeding -500 overdraft
            'adjustment_reason': 'Large deduction test'
        }

        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('overdraft', str(response.data))

    def test_adjustment_reason_required(self):
        """Test that adjustment reason is required."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('accounts:account_balance_update', kwargs={'pk': self.test_account.id})

        data = {
            'adjustment_amount': '100.00'
            # Missing adjustment_reason
        }

        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class AccountCloseAPITestCase(APITestCase):
    """Test cases for Account Closure API."""

    def setUp(self):
        """Set up test data."""
        self.customer_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.Role.CUSTOMER,
            status=User.Status.ACTIVE
        )

        self.staff_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.Role.STAFF,
            status=User.Status.ACTIVE
        )

        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.Role.ADMIN,
            status=User.Status.ACTIVE
        )

        self.zero_balance_account = Account.objects.create(
            user=self.customer_user,
            account_number='************',
            account_type=Account.AccountType.SAVINGS,
            balance=Decimal('0.00'),
            status=Account.Status.ACTIVE
        )

        self.positive_balance_account = Account.objects.create(
            user=self.customer_user,
            account_number='************',
            account_type=Account.AccountType.CHECKING,
            balance=Decimal('500.00'),
            status=Account.Status.ACTIVE
        )

    def test_admin_can_close_zero_balance_account(self):
        """Test admin can permanently close account with zero balance."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('accounts:account_close', kwargs={'pk': self.zero_balance_account.id})

        data = {
            'closure_type': 'close',
            'status_reason': 'Customer requested account closure'
        }

        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('closed', response.data['message'])

        # Verify account is closed
        self.zero_balance_account.refresh_from_db()
        self.assertEqual(self.zero_balance_account.status, Account.Status.CLOSED)

    def test_admin_can_deactivate_account(self):
        """Test admin can deactivate account (temporary closure)."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('accounts:account_close', kwargs={'pk': self.positive_balance_account.id})

        data = {
            'closure_type': 'deactivate',
            'status_reason': 'Temporary suspension for investigation'
        }

        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('deactivated', response.data['message'])

        # Verify account is inactive
        self.positive_balance_account.refresh_from_db()
        self.assertEqual(self.positive_balance_account.status, Account.Status.INACTIVE)

    def test_cannot_close_account_with_balance(self):
        """Test cannot permanently close account with outstanding balance."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('accounts:account_close', kwargs={'pk': self.positive_balance_account.id})

        data = {
            'closure_type': 'close',
            'status_reason': 'Customer requested closure'
        }

        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('balance', str(response.data))

    def test_staff_cannot_permanently_close_account(self):
        """Test staff cannot permanently close accounts."""
        self.client.force_authenticate(user=self.staff_user)
        url = reverse('accounts:account_close', kwargs={'pk': self.zero_balance_account.id})

        data = {
            'closure_type': 'close',
            'status_reason': 'Test closure'
        }

        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn('administrator', str(response.data))

    def test_closure_reason_required(self):
        """Test that closure reason is required."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('accounts:account_close', kwargs={'pk': self.zero_balance_account.id})

        data = {
            'closure_type': 'close'
            # Missing status_reason
        }

        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('reason', str(response.data))

    def test_customer_cannot_close_account(self):
        """Test customers cannot close accounts."""
        self.client.force_authenticate(user=self.customer_user)
        url = reverse('accounts:account_close', kwargs={'pk': self.zero_balance_account.id})

        data = {
            'closure_type': 'deactivate',
            'status_reason': 'Self closure'
        }

        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)