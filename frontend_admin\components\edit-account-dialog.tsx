"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import adminApiService from "@/lib/adminApiService"
import { getCurrentUser } from "@/lib/auth"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Loader2 } from "lucide-react"
import { toast } from "@/hooks/use-toast"

interface Account {
  id: string
  account_number: string
  account_type: string
  balance: number
  status: string
}

interface EditAccountDialogProps {
  account: Account
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function EditAccountDialog({ account, open, onOpenChange }: EditAccountDialogProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    account_type: account.account_type,
    balance: account.balance.toString(),
    status: account.status,
  })

  const router = useRouter()
  const currentUser = getCurrentUser()

  // Role-based permissions
  const canEditAccountType = currentUser && ['admin', 'super_admin'].includes(currentUser.role)
  const canEditBalance = currentUser && ['manager', 'admin', 'super_admin'].includes(currentUser.role)
  const canEditStatus = currentUser && ['staff', 'manager', 'admin', 'super_admin'].includes(currentUser.role)
  const canCloseAccount = currentUser && ['admin', 'super_admin'].includes(currentUser.role)

  useEffect(() => {
    setFormData({
      account_type: account.account_type,
      balance: account.balance.toString(),
      status: account.status,
    })
  }, [account])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Check if user has permission to update accounts
      if (!currentUser || !['staff', 'manager', 'admin', 'super_admin'].includes(currentUser.role)) {
        throw new Error('Insufficient permissions to update accounts')
      }

      // Prepare update data
      const updateData = {
        account_type: formData.account_type as 'savings' | 'checking' | 'business',
        balance: formData.balance,
        status: formData.status as 'active' | 'inactive' | 'suspended' | 'closed'
      }

      // Call Django API to update account
      const response = await adminApiService.updateAccount(account.id, updateData)

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to update account')
      }

      toast({
        title: "Success",
        description: "Account updated successfully",
      })

      onOpenChange(false)
      router.refresh()
    } catch (error: any) {
      console.error('Account update error:', error)

      let errorMessage = "Failed to update account"

      // Handle specific error types
      if (error.message?.includes('permission')) {
        errorMessage = "You don't have permission to update accounts"
      } else if (error.details) {
        // Handle field-specific validation errors from Django
        const fieldErrors = Object.entries(error.details)
          .map(([field, errors]) => `${field}: ${Array.isArray(errors) ? errors.join(', ') : errors}`)
          .join('; ')
        errorMessage = fieldErrors || errorMessage
      } else if (error.message) {
        errorMessage = error.message
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Account</DialogTitle>
          <DialogDescription>Update account information for {account.account_number}.</DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="account_type">Account Type</Label>
              <Select
                value={formData.account_type}
                onValueChange={(value) => setFormData({ ...formData, account_type: value })}
                disabled={!canEditAccountType}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="checking">Checking</SelectItem>
                  <SelectItem value="savings">Savings</SelectItem>
                  <SelectItem value="business">Business</SelectItem>
                </SelectContent>
              </Select>
              {!canEditAccountType && (
                <p className="text-xs text-muted-foreground">
                  Only admins can change account type
                </p>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="balance">Balance</Label>
              <Input
                id="balance"
                type="number"
                step="0.01"
                value={formData.balance}
                onChange={(e) => setFormData({ ...formData, balance: e.target.value })}
                disabled={!canEditBalance}
              />
              {!canEditBalance && (
                <p className="text-xs text-muted-foreground">
                  Only managers and above can adjust balance
                </p>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => setFormData({ ...formData, status: value })}
                disabled={!canEditStatus}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                  {canCloseAccount && <SelectItem value="closed">Closed</SelectItem>}
                </SelectContent>
              </Select>
              {!canEditStatus && (
                <p className="text-xs text-muted-foreground">
                  Only staff and above can change account status
                </p>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Update Account
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
