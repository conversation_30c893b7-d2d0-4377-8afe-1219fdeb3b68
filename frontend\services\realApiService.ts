/**
 * Real API Service for ExoBank Frontend
 * 
 * This service provides real HTTP requests to the Django backend API
 * to replace mock data functionality.
 * 
 * Features:
 * - Comprehensive error handling with retry logic
 * - Loading state management with progress indicators
 * - Offline request queueing and synchronization
 * - Token refresh and authentication management
 * - Standardized API response format
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform, Alert } from 'react-native';
import networkService from './networkService';
import loadingService from './loadingService';
import errorService, { ErrorType } from './errorService';

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  details?: any;
}

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// Data Types matching backend API
export interface AuthUser {
  id: string;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  phone_number: string;
  is_active: boolean;
  date_joined: string;
  profile?: {
    avatar_url?: string;
    date_of_birth?: string;
    address?: string;
    city?: string;
    country?: string;
  };
}

export interface Account {
  id: string;
  user: string;
  user_email: string;
  user_name: string;
  account_number: string;
  account_type: 'savings' | 'checking' | 'business';
  balance: string;
  balance_display: string;
  currency: string;
  status: 'active' | 'inactive' | 'suspended' | 'closed';
  created_at: string;
  updated_at: string;
}

export interface Transaction {
  id: string;
  user: string;
  user_email: string;
  from_account: string;
  from_account_number: string;
  from_account_type: string;
  to_account: string | null;
  to_account_number: string | null;
  to_account_type: string | null;
  transaction_type: 'deposit' | 'withdrawal' | 'transfer' | 'payment';
  transaction_type_display: string;
  amount: string;
  amount_display: string;
  currency: string;
  description: string;
  reference_number: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  status_display: string;
  created_at: string;
  updated_at: string;
  processed_at: string | null;
}

export interface Beneficiary {
  id: string;
  user: string;
  name: string;
  account_number: string;
  bank_name: string;
  routing_number: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Request Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  password_confirm: string;
  first_name: string;
  last_name: string;
  phone_number?: string;
}

export interface LoginResponse {
  user: AuthUser;
  access: string;
  refresh: string;
  message: string;
}

// Network Error Types
export interface NetworkError {
  message: string;
  status?: number;
  code?: string;
  isNetworkError: boolean;
}

class RealApiService {
  private baseURL: string;
  private maxRetries: number = 3;
  private retryDelay: number = 1000;

  constructor() {
    // Get API URL based on platform
    const apiUrl = Platform.OS === 'android' 
      ? process.env.EXPO_PUBLIC_API_URL_ANDROID 
      : process.env.EXPO_PUBLIC_API_URL;
    
    this.baseURL = apiUrl || 'http://127.0.0.1:8000';
  }

  // Utility method to handle network delays and retries
  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Get stored JWT token
  private async getAuthToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem('auth_token');
    } catch (error) {
      console.error('Error getting auth token:', error);
      return null;
    }
  }

  // Create request headers with authentication
  private async createHeaders(includeAuth: boolean = true): Promise<HeadersInit> {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (includeAuth) {
      const token = await this.getAuthToken();
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    }

    return headers;
  }

  // Generic HTTP request method with retry logic, offline queueing, and error handling
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
    includeAuth: boolean = true,
    offlineOptions: {
      queueOffline?: boolean;
      requestId?: string;
      expiresAfter?: number; // milliseconds
    } = {}
  ): Promise<ApiResponse<T>> {
    const method = options.method || 'GET';
    const requestId = offlineOptions.requestId || `${method}-${endpoint}-${Date.now()}`;
    
    const context = {
      component: 'RealApiService',
      action: `${method} ${endpoint}`,
      additionalData: {
        endpoint,
        includeAuth,
        hasBody: !!options.body,
        isOfflineQueued: offlineOptions.queueOffline
      }
    };

    // Check if we should queue this request when offline
    if (offlineOptions.queueOffline && !networkService.isOnline()) {
      console.log(`Network offline, queueing request: ${requestId}`);
      
      try {
        return await networkService.queueOfflineRequest<ApiResponse<T>>(
          requestId,
          () => this.makeRequest<T>(endpoint, options, includeAuth, { ...offlineOptions, queueOffline: false })
        );
      } catch (error: any) {
        const standardizedError = errorService.handleError(error, {
          ...context,
          additionalData: { ...context.additionalData, queueError: true }
        });
        
        return {
          success: false,
          error: 'Request queued for when network is available',
          details: {
            errorType: ErrorType.NETWORK,
            code: 'OFFLINE_QUEUED',
            timestamp: Date.now(),
            isNetworkError: true,
            queuedRequestId: requestId
          },
        };
      }
    }

    try {
      // Check for token expiration and refresh if needed
      if (includeAuth && method !== 'POST' && endpoint !== '/api/auth/refresh/') {
        await this.checkAndRefreshTokenIfNeeded();
      }

      return await networkService.executeWithRetry(async () => {
        const url = `${this.baseURL}${endpoint}`;
        const headers = await this.createHeaders(includeAuth);

        const response = await fetch(url, {
          ...options,
          headers: {
            ...headers,
            ...options.headers,
          },
        });

        // Handle different response types
        let responseData: any;
        const contentType = response.headers.get('content-type');
        
        if (contentType && contentType.includes('application/json')) {
          responseData = await response.json();
        } else {
          responseData = await response.text();
        }

        // Handle successful responses
        if (response.ok) {
          return {
            success: true,
            data: responseData,
          };
        }

        // Handle authentication errors - attempt token refresh
        if (response.status === 401 && includeAuth && endpoint !== '/api/auth/token/refresh/') {
          const refreshResult = await this.refreshToken();
          if (refreshResult.success) {
            // Retry the original request with new token
            return await this.makeRequest<T>(endpoint, options, includeAuth);
          }
        }

        // Handle error responses - create standardized error
        const error: any = new Error(
          responseData?.error?.message || 
          responseData?.message || 
          responseData?.detail || 
          `HTTP ${response.status}: ${response.statusText}`
        );
        error.status = response.status;
        error.response = responseData;
        
        // Add validation errors if present
        if (responseData?.errors || responseData?.non_field_errors) {
          error.validationErrors = responseData.errors || responseData.non_field_errors;
        }
        
        throw error;
      });
    } catch (error: any) {
      // Use error service to handle and standardize the error
      const standardizedError = errorService.handleError(error, context);
      
      // Show network error alert for network issues
      if (errorService.isNetworkError(standardizedError)) {
        networkService.showNetworkErrorAlert(error, () => {
          // Retry logic will be handled by the caller
        });
      }
      
      return {
        success: false,
        error: errorService.getUserFriendlyMessage(standardizedError),
        details: {
          errorType: standardizedError.type,
          code: standardizedError.code,
          timestamp: standardizedError.timestamp,
          isNetworkError: errorService.isNetworkError(standardizedError),
          status: error.status,
          originalError: error.message,
          response: error.response,
          validationErrors: standardizedError.details
        },
      };
    }
  }
  
  // Check if token needs refresh and refresh if needed
  private async checkAndRefreshTokenIfNeeded(): Promise<boolean> {
    try {
      // Simple token expiration check
      // In a real implementation, you would decode the JWT and check its expiration
      const token = await AsyncStorage.getItem('auth_token');
      if (!token) {
        return false;
      }
      
      // For now, we'll just check if we can get the current user
      // A more robust solution would decode the JWT and check its expiration
      const response = await this.makeRequest<any>(
        '/api/auth/profile/',
        {},
        true,
        { queueOffline: false } // Don't queue this check
      );
      
      if (!response.success && response.details?.status === 401) {
        // Token expired, try to refresh
        const refreshResult = await this.refreshToken();
        return refreshResult.success;
      }
      
      return true;
    } catch (error) {
      console.error('Error checking token:', error);
      return false;
    }
  }



  // Authentication methods
  async login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    return await loadingService.withLoading('auth-login', async () => {
      const response = await this.makeRequest<any>(
        '/api/auth/login/',
        {
          method: 'POST',
          body: JSON.stringify(credentials),
        },
        false // Don't include auth for login
      );

      if (response.success && response.data) {
        // Handle nested response structure from Django backend
        const loginData = response.data.data || response.data;
        
        // Store tokens
        await AsyncStorage.setItem('auth_token', loginData.access);
        await AsyncStorage.setItem('refresh_token', loginData.refresh);
        await AsyncStorage.setItem('user_data', JSON.stringify(loginData.user));

        return {
          success: true,
          data: {
            user: loginData.user,
            access: loginData.access,
            refresh: loginData.refresh,
            message: response.data.message || 'Login successful',
          },
        };
      }

      return response as ApiResponse<LoginResponse>;
    }, { 
      operation: 'login', 
      message: 'Signing in...' 
    });
  }

  async register(userData: RegisterRequest): Promise<ApiResponse<{ user: AuthUser }>> {
    return await loadingService.withLoading('auth-register', async () => {
      const response = await this.makeRequest<any>(
        '/api/auth/register/',
        {
          method: 'POST',
          body: JSON.stringify(userData),
        },
        false // Don't include auth for registration
      );

      if (response.success && response.data) {
        // Handle nested response structure from Django backend
        const registerData = response.data.data || response.data;
        
        return {
          success: true,
          data: {
            user: registerData.user
          },
        };
      }

      return response;
    }, { 
      operation: 'register', 
      message: 'Creating account...' 
    });
  }

  async logout(): Promise<ApiResponse<{}>> {
    return await loadingService.withLoading('auth-logout', async () => {
      const response = await this.makeRequest<{}>(
        '/api/auth/logout/',
        {
          method: 'POST',
        }
      );

      // Clear stored data regardless of response
      await AsyncStorage.multiRemove(['auth_token', 'refresh_token', 'user_data']);

      return response;
    }, { 
      operation: 'logout', 
      message: 'Signing out...' 
    });
  }

  async refreshToken(): Promise<ApiResponse<{ access: string }>> {
    try {
      const refreshToken = await AsyncStorage.getItem('refresh_token');
      if (!refreshToken) {
        return {
          success: false,
          error: 'No refresh token available',
        };
      }

      const response = await this.makeRequest<any>(
        '/api/auth/token/refresh/',
        {
          method: 'POST',
          body: JSON.stringify({ refresh: refreshToken }),
        },
        false // Don't include auth for token refresh
      );

      if (response.success && response.data) {
        // Handle nested response structure from Django backend
        const tokenData = response.data.data || response.data;
        
        // Store new access token
        await AsyncStorage.setItem('auth_token', tokenData.access);
        
        return {
          success: true,
          data: {
            access: tokenData.access
          }
        };
      }

      return response;
    } catch (error) {
      return {
        success: false,
        error: 'Token refresh failed',
      };
    }
  }

  // Account methods
  async getAccounts(): Promise<ApiResponse<Account[]>> {
    return await loadingService.withLoading('accounts-fetch', async () => {
      // Try to get cached accounts first
      const cachedAccounts = await this.getCachedData<Account[]>('accounts');
      
      // Make API request with offline queueing
      const response = await this.makeRequest<PaginatedResponse<Account>>(
        '/api/accounts/',
        {},
        true,
        { 
          queueOffline: true,
          requestId: 'get-accounts'
        }
      );
      
      if (response.success && response.data) {
        // Cache the accounts data
        await this.cacheData('accounts', response.data.results);
        
        return {
          success: true,
          data: response.data.results,
        };
      }
      
      // Return cached data if request failed and we have cached data
      if (!response.success && cachedAccounts) {
        return {
          success: true,
          data: cachedAccounts,
          details: {
            fromCache: true,
            timestamp: await this.getCacheTimestamp('accounts'),
            originalError: response.error
          }
        };
      }

      return {
        success: false,
        error: response.error,
        details: response.details,
      };
    }, { 
      operation: 'fetch-accounts', 
      message: 'Loading accounts...' 
    });
  }

  async getAccountById(accountId: string): Promise<ApiResponse<Account>> {
    return await loadingService.withLoading(`account-${accountId}`, async () => {
      // Try to get cached account first
      const cachedAccounts = await this.getCachedData<Account[]>('accounts');
      const cachedAccount = cachedAccounts?.find(acc => acc.id === accountId);
      
      // Make API request with offline queueing
      const response = await this.makeRequest<Account>(
        `/api/accounts/${accountId}/`,
        {},
        true,
        { 
          queueOffline: true,
          requestId: `get-account-${accountId}`
        }
      );
      
      if (response.success && response.data) {
        // Update the account in the cache
        if (cachedAccounts) {
          const updatedAccounts = cachedAccounts.map(acc => 
            acc.id === accountId ? response.data : acc
          );
          await this.cacheData('accounts', updatedAccounts);
        }
        
        return response;
      }
      
      // Return cached account if request failed and we have cached data
      if (!response.success && cachedAccount) {
        return {
          success: true,
          data: cachedAccount,
          details: {
            fromCache: true,
            timestamp: await this.getCacheTimestamp('accounts'),
            originalError: response.error
          }
        };
      }

      return response;
    }, { 
      operation: 'fetch-account', 
      message: 'Loading account details...' 
    });
  }

  async createAccount(accountData: {
    account_type: 'savings' | 'checking' | 'business';
    currency?: string;
    initial_deposit?: number;
  }): Promise<ApiResponse<Account>> {
    return await loadingService.withLoading('account-create', async () => {
      const response = await this.makeRequest<any>(
        '/api/accounts/create/',
        {
          method: 'POST',
          body: JSON.stringify(accountData),
        },
        true,
        {
          queueOffline: true,
          requestId: 'create-account'
        }
      );

      if (response.success && response.data) {
        // Handle nested response structure from Django backend
        const accountResponse = response.data.data || response.data;

        // Invalidate accounts cache to force refresh
        await this.clearCachedData('accounts');

        return {
          success: true,
          data: accountResponse,
        };
      }

      return response;
    }, {
      operation: 'create-account',
      message: 'Creating account...'
    });
  }

  // Transaction methods
  async getTransactions(accountId?: string): Promise<ApiResponse<Transaction[]>> {
    const loadingKey = accountId ? `transactions-${accountId}` : 'transactions-all';
    const cacheKey = accountId ? `transactions-${accountId}` : 'transactions-all';
    
    return await loadingService.withLoading(loadingKey, async () => {
      // Try to get cached transactions first
      const cachedTransactions = await this.getCachedData<Transaction[]>(cacheKey);
      
      // Build endpoint
      let endpoint = '/api/transactions/';
      if (accountId) {
        endpoint += `?from_account=${accountId}`;
      }

      // Make API request with offline queueing
      const response = await this.makeRequest<PaginatedResponse<Transaction>>(
        endpoint,
        {},
        true,
        { 
          queueOffline: true,
          requestId: `get-transactions-${accountId || 'all'}`
        }
      );
      
      if (response.success && response.data) {
        // Cache the transactions data
        await this.cacheData(cacheKey, response.data.results);
        
        return {
          success: true,
          data: response.data.results,
        };
      }
      
      // Return cached data if request failed and we have cached data
      if (!response.success && cachedTransactions) {
        return {
          success: true,
          data: cachedTransactions,
          details: {
            fromCache: true,
            timestamp: await this.getCacheTimestamp(cacheKey),
            originalError: response.error
          }
        };
      }

      return {
        success: false,
        error: response.error,
        details: response.details,
      };
    }, { 
      operation: 'fetch-transactions', 
      message: 'Loading transactions...' 
    });
  }

  async getTransactionById(transactionId: string): Promise<ApiResponse<Transaction>> {
    return await loadingService.withLoading(`transaction-${transactionId}`, async () => {
      // Try to find transaction in cache first
      const cachedTransactionsAll = await this.getCachedData<Transaction[]>('transactions-all');
      let cachedTransaction = cachedTransactionsAll?.find(tx => tx.id === transactionId);
      
      if (!cachedTransaction) {
        // Try to find in account-specific caches
        const cacheKeys = await this.getAllCacheKeys();
        for (const key of cacheKeys) {
          if (key.startsWith('transactions-') && key !== 'transactions-all') {
            const accountTransactions = await this.getCachedData<Transaction[]>(key);
            const found = accountTransactions?.find(tx => tx.id === transactionId);
            if (found) {
              cachedTransaction = found;
              break;
            }
          }
        }
      }
      
      // Make API request with offline queueing
      const response = await this.makeRequest<Transaction>(
        `/api/transactions/${transactionId}/`,
        {},
        true,
        { 
          queueOffline: true,
          requestId: `get-transaction-${transactionId}`
        }
      );
      
      if (response.success && response.data) {
        return response;
      }
      
      // Return cached transaction if request failed and we have cached data
      if (!response.success && cachedTransaction) {
        return {
          success: true,
          data: cachedTransaction,
          details: {
            fromCache: true,
            originalError: response.error
          }
        };
      }

      return response;
    }, { 
      operation: 'fetch-transaction', 
      message: 'Loading transaction details...' 
    });
  }

  // Beneficiary methods
  async getBeneficiaries(): Promise<ApiResponse<Beneficiary[]>> {
    return await loadingService.withLoading('beneficiaries-fetch', async () => {
      // Try to get cached beneficiaries first
      const cachedBeneficiaries = await this.getCachedData<Beneficiary[]>('beneficiaries');
      
      // Make API request with offline queueing
      const response = await this.makeRequest<PaginatedResponse<Beneficiary>>(
        '/api/accounts/beneficiaries/',
        {},
        true,
        { 
          queueOffline: true,
          requestId: 'get-beneficiaries'
        }
      );
      
      if (response.success && response.data) {
        // Cache the beneficiaries data
        await this.cacheData('beneficiaries', response.data.results);
        
        return {
          success: true,
          data: response.data.results,
        };
      }
      
      // Return cached data if request failed and we have cached data
      if (!response.success && cachedBeneficiaries) {
        return {
          success: true,
          data: cachedBeneficiaries,
          details: {
            fromCache: true,
            timestamp: await this.getCacheTimestamp('beneficiaries'),
            originalError: response.error
          }
        };
      }

      return {
        success: false,
        error: response.error,
        details: response.details,
      };
    }, { 
      operation: 'fetch-beneficiaries', 
      message: 'Loading beneficiaries...' 
    });
  }

  async createBeneficiary(beneficiaryData: Omit<Beneficiary, 'id' | 'user' | 'created_at' | 'updated_at'>): Promise<ApiResponse<Beneficiary>> {
    return await loadingService.withLoading('beneficiary-create', async () => {
      // Generate a temporary ID for offline operations
      const tempId = `temp_${Date.now()}`;
      
      // Make API request with offline queueing
      const response = await this.makeRequest<Beneficiary>(
        '/api/accounts/beneficiaries/',
        {
          method: 'POST',
          body: JSON.stringify(beneficiaryData),
        },
        true,
        {
          queueOffline: true,
          requestId: `create-beneficiary-${tempId}`
        }
      );
      
      if (response.success && response.data) {
        // Update the beneficiaries cache
        const cachedBeneficiaries = await this.getCachedData<Beneficiary[]>('beneficiaries') || [];
        await this.cacheData('beneficiaries', [...cachedBeneficiaries, response.data]);
        
        // Show success message
        Alert.alert('Success', 'Beneficiary added successfully');
      } else if (!response.success && response.details?.code === 'OFFLINE_QUEUED') {
        // Create a temporary beneficiary for offline mode
        const tempBeneficiary: Beneficiary = {
          id: tempId,
          user: 'offline_user',
          name: beneficiaryData.name,
          account_number: beneficiaryData.account_number,
          bank_name: beneficiaryData.bank_name,
          routing_number: beneficiaryData.routing_number,
          is_active: beneficiaryData.is_active,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };
        
        // Add to cache for offline mode
        const cachedBeneficiaries = await this.getCachedData<Beneficiary[]>('beneficiaries') || [];
        await this.cacheData('beneficiaries', [...cachedBeneficiaries, tempBeneficiary]);
        
        return {
          success: true,
          data: tempBeneficiary,
          details: {
            isOffline: true,
            tempId: tempId
          }
        };
      }
      
      return response;
    }, { 
      operation: 'create-beneficiary', 
      message: 'Adding beneficiary...' 
    });
  }

  // User methods
  async getCurrentUser(): Promise<ApiResponse<AuthUser>> {
    // First try to get from storage
    try {
      const userData = await AsyncStorage.getItem('user_data');
      if (userData) {
        const user = JSON.parse(userData);
        return {
          success: true,
          data: user,
        };
      }
    } catch (error) {
      console.error('Error getting user from storage:', error);
    }

    // If not in storage, fetch from API
    const response = await this.makeRequest<any>('/api/auth/profile/');
    
    if (response.success && response.data) {
      // Handle nested response structure from Django backend
      const userData = response.data.data || response.data;
      
      return {
        success: true,
        data: userData
      };
    }
    
    return response;
  }

  // Data caching methods
  private async cacheData<T>(key: string, data: T): Promise<void> {
    try {
      const cacheItem = {
        data,
        timestamp: Date.now()
      };
      await AsyncStorage.setItem(`cache_${key}`, JSON.stringify(cacheItem));
    } catch (error) {
      console.error(`Error caching data for ${key}:`, error);
    }
  }

  private async getCachedData<T>(key: string): Promise<T | null> {
    try {
      const cachedItem = await AsyncStorage.getItem(`cache_${key}`);
      if (cachedItem) {
        const { data } = JSON.parse(cachedItem);
        return data as T;
      }
      return null;
    } catch (error) {
      console.error(`Error getting cached data for ${key}:`, error);
      return null;
    }
  }

  private async getCacheTimestamp(key: string): Promise<number | null> {
    try {
      const cachedItem = await AsyncStorage.getItem(`cache_${key}`);
      if (cachedItem) {
        const { timestamp } = JSON.parse(cachedItem);
        return timestamp;
      }
      return null;
    } catch (error) {
      console.error(`Error getting cache timestamp for ${key}:`, error);
      return null;
    }
  }

  private async isCacheExpired(key: string, maxAge: number): Promise<boolean> {
    const timestamp = await this.getCacheTimestamp(key);
    if (!timestamp) return true;
    
    const now = Date.now();
    return (now - timestamp) > maxAge;
  }

  private async clearCache(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(`cache_${key}`);
    } catch (error) {
      console.error(`Error clearing cache for ${key}:`, error);
    }
  }

  async clearAllCaches(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('cache_'));
      await AsyncStorage.multiRemove(cacheKeys);
    } catch (error) {
      console.error('Error clearing all caches:', error);
    }
  }

  private async getAllCacheKeys(): Promise<string[]> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      return keys
        .filter(key => key.startsWith('cache_'))
        .map(key => key.replace('cache_', ''));
    } catch (error) {
      console.error('Error getting all cache keys:', error);
      return [];
    }
  }

  // Utility methods
  async isAuthenticated(): Promise<boolean> {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      if (!token) {
        return false;
      }

      // Verify token with backend
      const response = await this.makeRequest<any>(
        '/api/auth/profile/',
        {},
        true,
        { queueOffline: false } // Don't queue this check
      );
      return response.success;
    } catch (error) {
      return false;
    }
  }

  async saveUser(user: AuthUser): Promise<void> {
    try {
      await AsyncStorage.setItem('user_data', JSON.stringify(user));
    } catch (error) {
      console.error('Error saving user data:', error);
    }
  }

  async saveToken(token: string): Promise<void> {
    try {
      await AsyncStorage.setItem('auth_token', token);
    } catch (error) {
      console.error('Error saving token:', error);
    }
  }

  async getStoredToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem('auth_token');
    } catch (error) {
      console.error('Error getting stored token:', error);
      return null;
    }
  }

  async getStoredUser(): Promise<AuthUser | null> {
    try {
      const userData = await AsyncStorage.getItem('user_data');
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error getting stored user:', error);
      return null;
    }
  }

  async clearAuthData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove(['auth_token', 'refresh_token', 'user_data']);
    } catch (error) {
      console.error('Error clearing auth data:', error);
    }
  }
  
  // Network status methods
  isOnline(): boolean {
    return networkService.isOnline();
  }
  
  getNetworkState() {
    return networkService.getNetworkState();
  }
  
  addNetworkListener(listener: (state: any) => void) {
    return networkService.addNetworkListener(listener);
  }
}

// Export singleton instance
const realApiService = new RealApiService();
export default realApiService;