import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions, Animated, Platform } from 'react-native';
import { Eye, EyeOff, AlertCircle } from 'lucide-react-native';
import { DisplayAccount } from '@/types';
import { useTheme } from '@/hooks/useTheme';
import ErrorBoundary from './ErrorBoundary';
import { pathResolver } from '@/utils/pathResolver';

interface AccountCardProps {
  account: DisplayAccount;
  onToggleVisibility: (id: string) => void;
  onPress: (account: DisplayAccount) => void;
}

const { width } = Dimensions.get('window');
const cardWidth = width * 0.85;

function AccountCardContent({ account, onToggleVisibility, onPress }: AccountCardProps) {
  const { theme } = useTheme();
  const scaleValue = new Animated.Value(1);
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Validate resources on mount to prevent ENOENT errors
  useEffect(() => {
    const validateResources = async () => {
      try {
        // For web platform, skip file validation as it's not supported
        if (Platform.OS === 'web') {
          return;
        }
        
        // Validate any file paths that might be used in the component
        // This is a preventive measure against ENOENT errors
        await pathResolver.validatePath('components/AccountCard.tsx');
      } catch (error) {
        console.error('Error validating resources for AccountCard:', error);
        // Don't set error state for path validation failures on web
        if (Platform.OS !== 'web') {
          setHasError(true);
          setErrorMessage(error instanceof Error ? error.message : 'Unknown error');
        }
      }
    };

    validateResources();
  }, []);

  const handlePressIn = () => {
    try {
      Animated.spring(scaleValue, {
        toValue: 0.98,
        useNativeDriver: true,
      }).start();
    } catch (error) {
      console.error('Error in handlePressIn:', error);
    }
  };

  const handlePressOut = () => {
    try {
      Animated.spring(scaleValue, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    } catch (error) {
      console.error('Error in handlePressOut:', error);
    }
  };

  const getCardColor = (type: string) => {
    switch (type) {
      case 'Savings':
        return theme.primary;
      case 'FD':
        return theme.info;
      case 'RD':
        return theme.warning;
      case 'Loan':
        return theme.secondary;
      default:
        return theme.primary;
    }
  };

  const formatAmount = (amount: number) => {
    return account.isVisible ? `₹${Math.abs(amount).toLocaleString('en-IN')}` : '₹****';
  };

  const styles = StyleSheet.create({
    card: {
      width: cardWidth,
      marginHorizontal: 10,
      padding: 16,
      borderRadius: 16,
      elevation: 3,
      shadowColor: theme.shadow,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.22,
      shadowRadius: 2.22,
      minHeight: 100,
      backgroundColor: getCardColor(account.type),
    },
    errorCard: {
      backgroundColor: theme.surfaceVariant,
      borderWidth: 1,
      borderColor: theme.error,
    },
    errorContent: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: 16,
    },
    errorIcon: {
      marginBottom: 8,
    },
    errorText: {
      fontSize: 14,
      fontWeight: 'bold',
      color: theme.error,
      marginBottom: 4,
      textAlign: 'center',
    },
    errorDetail: {
      fontSize: 12,
      color: theme.textSecondary,
      textAlign: 'center',
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    accountInfo: {
      flex: 1,
    },
    accountType: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.textOnPrimary,
      marginBottom: 2,
    },
    accountNumber: {
      fontSize: 12,
      color: theme.textOnPrimary,
      opacity: 0.8,
    },
    eyeButton: {
      padding: 8,
      marginTop: -8,
      marginRight: -8,
    },
    balanceContainer: {
      flex: 1,
      justifyContent: 'flex-end',
    },
    balanceLabel: {
      fontSize: 10,
      color: theme.textOnPrimary,
      opacity: 0.7,
      marginBottom: 2,
      textTransform: 'uppercase',
      letterSpacing: 0.5,
    },
    balance: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.textOnPrimary,
    },
    interestRate: {
      fontSize: 10,
      color: theme.textOnPrimary,
      opacity: 0.8,
      marginTop: 2,
    },
  });

  // If there's an error loading resources, show a fallback UI
  if (hasError) {
    return (
      <View style={[styles.card, styles.errorCard]}>
        <View style={styles.errorContent}>
          <AlertCircle size={24} color={theme.error} style={styles.errorIcon} />
          <Text style={styles.errorText}>Failed to load account card</Text>
          <Text style={styles.errorDetail}>{errorMessage || 'Unknown error'}</Text>
        </View>
      </View>
    );
  }

  // Safe rendering with try-catch to prevent runtime errors
  try {
    return (
      <Animated.View style={[{ transform: [{ scale: scaleValue }] }]}>
        <TouchableOpacity
          style={styles.card}
          onPress={() => onPress(account)}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={1}
        >
          <View style={styles.header}>
            <View style={styles.accountInfo}>
              <Text style={styles.accountType}>{account.type}</Text>
              <Text style={styles.accountNumber}>{account.accountNumber}</Text>
            </View>
            <TouchableOpacity
              onPress={() => onToggleVisibility(account.id)}
              style={styles.eyeButton}
            >
              {account.isVisible ? (
                <Eye size={18} color={theme.textOnPrimary} />
              ) : (
                <EyeOff size={18} color={theme.textOnPrimary} />
              )}
            </TouchableOpacity>
          </View>
          
          <View style={styles.balanceContainer}>
            <Text style={styles.balanceLabel}>
              {account.type === 'Loan' ? 'Outstanding' : 'Available Balance'}
            </Text>
            <Text style={styles.balance}>{formatAmount(account.balance)}</Text>
            {account.interestRate && (
              <Text style={styles.interestRate}>
                {account.interestRate}% p.a.
              </Text>
            )}
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  } catch (error) {
    console.error('Error rendering AccountCard:', error);
    return (
      <View style={[styles.card, styles.errorCard]}>
        <View style={styles.errorContent}>
          <AlertCircle size={24} color={theme.error} style={styles.errorIcon} />
          <Text style={styles.errorText}>Failed to render account card</Text>
        </View>
      </View>
    );
  }
}

// Export the component wrapped in an ErrorBoundary
export default function AccountCard(props: AccountCardProps) {
  return (
    <ErrorBoundary componentName="AccountCard">
      <AccountCardContent {...props} />
    </ErrorBoundary>
  );
}