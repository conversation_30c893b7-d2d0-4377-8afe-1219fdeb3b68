/**
 * Real Authentication Service for ExoBank Admin Frontend
 *
 * This service provides real JWT authentication with the Django backend,
 * replacing the mock authentication system with actual API calls.
 */

import {
  User,
  AuthData,
  LoginRequest,
  LoginResponse,
  ApiResponse
} from './types'
import {
  validateJWTToken,
  isTokenExpired,
  shouldRefreshToken,
  getTokenExpirationTime,
  JWT_CONFIG,
  JWTError
} from '../utils/jwtUtils'

// Environment configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'
const AUTH_TOKEN_KEY = process.env.NEXT_PUBLIC_AUTH_TOKEN_KEY || 'exobank_admin_auth'
const TOKEN_REFRESH_THRESHOLD_MINUTES = parseInt(process.env.NEXT_PUBLIC_TOKEN_REFRESH_THRESHOLD_MINUTES || '5') // 5 minutes

// Authentication error types
enum AuthErrorCode {
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  ACCOUNT_INACTIVE = 'ACCOUNT_INACTIVE',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_INVALID = 'TOKEN_INVALID',
  TOKEN_REFRESH_FAILED = 'TOKEN_REFRESH_FAILED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

interface AuthError {
  code: AuthErrorCode;
  message: string;
  details?: any;
}

function createAuthError(code: AuthErrorCode, message: string, details?: any): AuthError {
  return { code, message, details };
}

// Auth Data Management
let authData: AuthData = {
  access: null,
  refresh: null,
  user: null
}

/**
 * Get authentication data from localStorage
 */
export function getAuthData(): AuthData {
  if (typeof window !== 'undefined') {
    try {
      const stored = localStorage.getItem(AUTH_TOKEN_KEY)
      if (stored) {
        const parsed = JSON.parse(stored)
        authData = parsed
        return parsed
      }
    } catch (error) {
      console.error('Failed to parse stored auth data:', error)
      clearAuthData()
    }
  }
  return authData
}

/**
 * Set authentication data and persist to localStorage
 */
export function setAuthData(data: AuthData): void {
  authData = data
  if (typeof window !== 'undefined') {
    if (data.access && data.user) {
      localStorage.setItem(AUTH_TOKEN_KEY, JSON.stringify(data))
    } else {
      localStorage.removeItem(AUTH_TOKEN_KEY)
    }
  }
}

/**
 * Clear authentication data
 */
export function clearAuthData(): void {
  authData = { access: null, refresh: null, user: null }
  if (typeof window !== 'undefined') {
    localStorage.removeItem(AUTH_TOKEN_KEY)
  }
}

/**
 * Check if user is authenticated with proper JWT validation
 */
export function isAuthenticated(): boolean {
  const auth = getAuthData()

  if (!auth.access || !auth.user) {
    return false
  }

  // Validate the access token using JWT utilities
  const validation = validateJWTToken(auth.access);
  return validation.isValid;
}

/**
 * Check if user is authenticated and has valid session
 */
export async function isAuthenticatedAsync(): Promise<boolean> {
  const auth = getAuthData()

  if (!auth.access || !auth.user) {
    return false
  }

  // Validate the access token
  const validation = validateJWTToken(auth.access);

  if (!validation.isValid) {
    // Token is invalid, try to refresh if we have a refresh token
    if (auth.refresh) {
      try {
        await refreshToken()
        return true
      } catch (error) {
        console.error('Token refresh failed during authentication check:', error)
        return false
      }
    }
    return false
  }

  // Check if token should be refreshed soon
  if (validation.shouldRefresh && auth.refresh) {
    try {
      await refreshToken()
    } catch (error) {
      console.warn('Background token refresh failed:', error)
      // Don't fail authentication if background refresh fails
    }
  }

  return true
}

/**
 * Get current authenticated user
 */
export function getCurrentUser(): User | null {
  const auth = getAuthData()
  return auth.user
}

/**
 * Check if token is expired or about to expire using JWT utilities
 */
function isTokenExpiredOrNeedsRefresh(token: string): boolean {
  // Use the imported JWT utilities for proper token validation
  const validation = validateJWTToken(token, TOKEN_REFRESH_THRESHOLD_MINUTES);
  return !validation.isValid || validation.shouldRefresh;
}

/**
 * Map API errors to auth error codes
 */
function mapApiErrorToAuthError(error: string | undefined): AuthErrorCode {
  if (!error) return AuthErrorCode.UNKNOWN_ERROR;

  const errorLower = error.toLowerCase();

  if (errorLower.includes('invalid') && errorLower.includes('credentials')) {
    return AuthErrorCode.INVALID_CREDENTIALS;
  }
  if (errorLower.includes('inactive')) {
    return AuthErrorCode.ACCOUNT_INACTIVE;
  }
  if (errorLower.includes('permission') || errorLower.includes('access')) {
    return AuthErrorCode.INSUFFICIENT_PERMISSIONS;
  }
  if (errorLower.includes('network')) {
    return AuthErrorCode.NETWORK_ERROR;
  }

  return AuthErrorCode.UNKNOWN_ERROR;
}

/**
 * Custom ApiError class
 */
class ApiError extends Error {
  code?: string
  status?: number
  details?: Record<string, string[]>
  timestamp?: string
  path?: string

  constructor(message: string) {
    super(message)
    this.name = 'ApiError'
  }
}

/**
 * Make authenticated API request with automatic token refresh
 */
async function makeAuthenticatedRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  const auth = getAuthData()
  
  if (!auth.access) {
    throw new ApiError('No access token available')
  }

  // Check if token needs refresh using JWT utilities
  if (isTokenExpiredOrNeedsRefresh(auth.access) && auth.refresh) {
    try {
      await refreshToken()
    } catch (error) {
      console.error('Token refresh failed:', error)
      clearAuthData()
      throw new ApiError('Authentication expired. Please log in again.')
    }
  }

  const url = `${API_BASE_URL}/api/${endpoint}`
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${getAuthData().access}`,
    ...options.headers
  }

  try {
    const response = await fetch(url, {
      ...options,
      headers
    })

    const data = await response.json()

    if (!response.ok) {
      const error = new ApiError(data.error?.message || 'Request failed')
      error.code = data.error?.code
      error.status = response.status
      error.details = data.error?.details
      throw error
    }

    return data
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    
    const apiError = new ApiError('Network error occurred')
    apiError.code = 'NETWORK_ERROR'
    throw apiError
  }
}

/**
 * Authenticate user with email and password
 */
export async function authenticateUser(email: string, password: string): Promise<User | null> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/auth/login/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email, password })
    })

    const data: ApiResponse<LoginResponse> = await response.json()

    if (!response.ok || !data.success || !data.data) {
      const errorCode = mapApiErrorToAuthError(data.error?.message);
      console.error('Login failed:', data.error?.message || 'Unknown error', { code: errorCode })
      return null
    }

    const { access, refresh, user } = data.data

    // Validate the received JWT token
    const tokenValidation = validateJWTToken(access);
    if (!tokenValidation.isValid) {
      console.error('Received invalid token from server')
      return null
    }

    // Verify user has admin access
    if (!['staff', 'manager', 'admin', 'super_admin'].includes(user.role)) {
      console.error('User does not have admin access')
      return null
    }

    // Store authentication data
    setAuthData({ access, refresh, user })

    return user
  } catch (error) {
    console.error('Authentication error:', error)
    return null
  }
}

/**
 * Refresh access token using refresh token
 */
export async function refreshToken(): Promise<void> {
  const auth = getAuthData()

  if (!auth.refresh) {
    throw new ApiError('No refresh token available')
  }

  try {
    const response = await fetch(`${API_BASE_URL}/api/auth/token/refresh/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ refresh: auth.refresh })
    })

    const data: ApiResponse<{ access: string; refresh?: string }> = await response.json()

    if (!response.ok || !data.success || !data.data) {
      const errorCode = mapApiErrorToAuthError(data.error?.message);
      throw new ApiError(data.error?.message || 'Token refresh failed')
    }

    // Validate the new access token
    const tokenValidation = validateJWTToken(data.data.access);
    if (!tokenValidation.isValid) {
      throw new ApiError('Received invalid token from refresh endpoint')
    }

    // Update stored tokens
    const newAuthData = {
      ...auth,
      access: data.data.access,
      refresh: data.data.refresh || auth.refresh
    }

    setAuthData(newAuthData)
  } catch (error) {
    console.error('Token refresh error:', error)
    clearAuthData()
    throw error
  }
}

/**
 * Logout user and invalidate tokens
 */
export async function logoutUser(): Promise<void> {
  const auth = getAuthData()
  
  try {
    if (auth.refresh) {
      await fetch(`${API_BASE_URL}/api/auth/logout/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${auth.access}`
        },
        body: JSON.stringify({ refresh: auth.refresh })
      })
    }
  } catch (error) {
    console.error('Logout error:', error)
  } finally {
    clearAuthData()
  }
}

/**
 * Validate admin access for current user
 */
export async function validateAdminAccess(): Promise<boolean> {
  try {
    const response = await makeAuthenticatedRequest<{ access: boolean }>('auth/access/admin-dashboard/')
    return response.success && response.data?.access === true
  } catch (error) {
    console.error('Admin access validation failed:', error)
    return false
  }
}

/**
 * Get user role information
 */
export async function getUserRole(): Promise<{
  role: string
  role_display: string
  permissions: string[]
} | null> {
  try {
    const response = await makeAuthenticatedRequest<{
      role: string
      permissions: string[]
    }>('auth/permissions/')
    
    if (response.success && response.data) {
      return {
        role: response.data.role,
        role_display: response.data.role.charAt(0).toUpperCase() + response.data.role.slice(1),
        permissions: response.data.permissions
      }
    }
    
    return null
  } catch (error) {
    console.error('Failed to get user role:', error)
    return null
  }
}

// Role validation functions
export const hasAdminAccess = (user: User | null): boolean => {
  if (!user) return false
  return ['staff', 'manager', 'admin', 'super_admin'].includes(user.role)
}

export const hasManagerAccess = (user: User | null): boolean => {
  if (!user) return false
  return ['manager', 'admin', 'super_admin'].includes(user.role)
}

export const isAdminUser = (user: User | null): boolean => {
  if (!user) return false
  return ['admin', 'super_admin'].includes(user.role)
}

export const isSuperAdmin = (user: User | null): boolean => {
  if (!user) return false
  return user.role === 'super_admin'
}

// Export ApiError for use in other modules
export { ApiError }