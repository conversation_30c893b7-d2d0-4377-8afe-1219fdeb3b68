/**
 * API Service for ExoBank Frontend
 *
 * This service can switch between mock data and real Django backend API
 * based on environment configuration.
 */

import mockApiService, { MockUser, MockAccount, MockTransaction, MockBeneficiary, ApiResponse as MockApiResponse } from './mockApiService';
import realApiService, { AuthUser as RealAuthUser, Account as RealAccount, Transaction as RealTransaction, Beneficiary as RealBeneficiary, ApiResponse as RealApiResponse } from './realApiService';

// Environment configuration
const USE_REAL_API = process.env.EXPO_PUBLIC_USE_REAL_API === 'true';

// Unified types (using real API types as the standard)
export type AuthUser = RealAuthUser;
export type Account = RealAccount;
export type Transaction = RealTransaction;
export type Beneficiary = RealBeneficiary;
export type ApiResponse<T> = RealApiResponse<T>;

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  password_confirm: string;
  first_name: string;
  last_name: string;
  phone_number?: string;
}

export interface LoginResponse {
  user: AuthUser;
  token: string;
  access?: string;
  refresh?: string;
  message: string;
}

// Type adapters to convert between mock and real API types
class TypeAdapter {
  static mockUserToReal(mockUser: MockUser): AuthUser {
    return {
      id: mockUser.id,
      email: mockUser.email,
      username: mockUser.username,
      first_name: mockUser.first_name,
      last_name: mockUser.last_name,
      phone_number: mockUser.phone_number,
      is_active: mockUser.is_active,
      date_joined: mockUser.date_joined,
      profile: mockUser.profile,
    };
  }

  static mockAccountToReal(mockAccount: MockAccount): Account {
    return {
      id: mockAccount.id,
      user: mockAccount.id, // Mock doesn't have user field
      user_email: '<EMAIL>', // Mock doesn't have this
      user_name: 'Mock User', // Mock doesn't have this
      account_number: mockAccount.account_number,
      account_type: mockAccount.account_type,
      balance: mockAccount.balance.toString(),
      balance_display: `$${mockAccount.balance.toLocaleString()}`,
      currency: mockAccount.currency,
      status: mockAccount.is_active ? 'active' : 'inactive',
      created_at: mockAccount.created_at,
      updated_at: mockAccount.updated_at,
    };
  }

  static mockTransactionToReal(mockTransaction: MockTransaction): Transaction {
    return {
      id: mockTransaction.id,
      user: mockTransaction.account_id, // Mock uses account_id
      user_email: '<EMAIL>', // Mock doesn't have this
      from_account: mockTransaction.account_id,
      from_account_number: 'ACC123456789', // Mock doesn't have this
      from_account_type: 'savings', // Mock doesn't have this
      to_account: mockTransaction.recipient?.account_number || null,
      to_account_number: mockTransaction.recipient?.account_number || null,
      to_account_type: null, // Mock doesn't have this
      transaction_type: mockTransaction.transaction_type === 'credit' ? 'deposit' : 'withdrawal',
      transaction_type_display: mockTransaction.transaction_type === 'credit' ? 'Deposit' : 'Withdrawal',
      amount: mockTransaction.amount.toString(),
      amount_display: `$${mockTransaction.amount.toLocaleString()}`,
      currency: mockTransaction.currency,
      description: mockTransaction.description,
      reference_number: mockTransaction.reference_number,
      status: mockTransaction.status,
      status_display: mockTransaction.status.charAt(0).toUpperCase() + mockTransaction.status.slice(1),
      created_at: mockTransaction.created_at,
      updated_at: mockTransaction.created_at, // Mock doesn't have updated_at
      processed_at: mockTransaction.status === 'completed' ? mockTransaction.created_at : null,
    };
  }

  static mockBeneficiaryToReal(mockBeneficiary: MockBeneficiary): Beneficiary {
    return {
      id: mockBeneficiary.id,
      user: mockBeneficiary.id, // Mock doesn't have user field
      name: mockBeneficiary.name,
      account_number: mockBeneficiary.account_number,
      bank_name: mockBeneficiary.bank_name,
      routing_number: mockBeneficiary.bank_code, // Using bank_code as routing_number
      is_active: mockBeneficiary.is_active,
      created_at: mockBeneficiary.created_at,
      updated_at: mockBeneficiary.created_at, // Mock doesn't have updated_at
    };
  }
}

class ApiService {
  private get apiService() {
    return USE_REAL_API ? realApiService : mockApiService;
  }

  constructor() {
    console.log(`API Service initialized with ${USE_REAL_API ? 'REAL' : 'MOCK'} backend`);
  }

  // Authentication methods
  async login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    if (USE_REAL_API) {
      const response = await realApiService.login(credentials);
      if (response.success && response.data) {
        return {
          success: true,
          data: {
            user: response.data.user,
            token: response.data.access, // Real API uses 'access' token
            message: response.data.message,
          }
        };
      }
      return response as ApiResponse<LoginResponse>;
    } else {
      const response = await mockApiService.login(credentials);
      if (response.success && response.data) {
        return {
          success: true,
          data: {
            user: TypeAdapter.mockUserToReal(response.data.user),
            token: response.data.token,
            message: 'Login successful'
          }
        };
      }
      return response as ApiResponse<LoginResponse>;
    }
  }

  async register(userData: RegisterRequest): Promise<ApiResponse<{ user: AuthUser }>> {
    if (USE_REAL_API) {
      return await realApiService.register(userData);
    } else {
      const mockData = {
        ...userData,
        phone_number: userData.phone_number || '', // Ensure phone_number is always a string
      };
      const response = await mockApiService.register(mockData);
      if (response.success && response.data) {
        return {
          success: true,
          data: { user: TypeAdapter.mockUserToReal(response.data.user) }
        };
      }
      return response as ApiResponse<{ user: AuthUser }>;
    }
  }

  async logout(): Promise<ApiResponse<{}>> {
    if (USE_REAL_API) {
      return await realApiService.logout();
    } else {
      return await mockApiService.logout();
    }
  }

  // Account methods
  async getAccounts(): Promise<ApiResponse<Account[]>> {
    if (USE_REAL_API) {
      return await realApiService.getAccounts();
    } else {
      const response = await mockApiService.getAccounts();
      if (response.success && response.data) {
        return {
          success: true,
          data: response.data.map(TypeAdapter.mockAccountToReal)
        };
      }
      return response as ApiResponse<Account[]>;
    }
  }

  async createAccount(accountData: {
    account_type: 'savings' | 'checking' | 'business';
    currency?: string;
    initial_deposit?: number;
  }): Promise<ApiResponse<Account>> {
    if (USE_REAL_API) {
      return await realApiService.createAccount(accountData);
    } else {
      // Mock implementation for development
      const mockAccount = {
        id: Date.now().toString(),
        account_number: `ACC${Date.now()}`,
        account_type: accountData.account_type,
        balance: accountData.initial_deposit?.toString() || '0.00',
        currency: accountData.currency || 'USD',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      return {
        success: true,
        data: TypeAdapter.mockAccountToReal(mockAccount as any)
      };
    }
  }

  // Transaction methods
  async getTransactions(accountId?: string): Promise<ApiResponse<Transaction[]>> {
    if (USE_REAL_API) {
      return await realApiService.getTransactions(accountId);
    } else {
      const response = await mockApiService.getTransactions(accountId);
      if (response.success && response.data) {
        return {
          success: true,
          data: response.data.map(TypeAdapter.mockTransactionToReal)
        };
      }
      return response as ApiResponse<Transaction[]>;
    }
  }

  // Beneficiary methods
  async getBeneficiaries(): Promise<ApiResponse<Beneficiary[]>> {
    if (USE_REAL_API) {
      return await realApiService.getBeneficiaries();
    } else {
      const response = await mockApiService.getBeneficiaries();
      if (response.success && response.data) {
        return {
          success: true,
          data: response.data.map(TypeAdapter.mockBeneficiaryToReal)
        };
      }
      return response as ApiResponse<Beneficiary[]>;
    }
  }

  // User methods
  async getCurrentUser(): Promise<ApiResponse<AuthUser>> {
    if (USE_REAL_API) {
      return await realApiService.getCurrentUser();
    } else {
      const response = await mockApiService.getCurrentUser();
      if (response.success && response.data) {
        return {
          success: true,
          data: TypeAdapter.mockUserToReal(response.data)
        };
      }
      return response as ApiResponse<AuthUser>;
    }
  }

  // Utility methods
  async isAuthenticated(): Promise<boolean> {
    if (USE_REAL_API) {
      return await realApiService.isAuthenticated();
    } else {
      return await mockApiService.isUserAuthenticated();
    }
  }

  async saveUser(user: AuthUser): Promise<void> {
    if (USE_REAL_API) {
      await realApiService.saveUser(user);
    } else {
      // Convert back to mock format for storage
      const mockUser: MockUser = {
        id: user.id,
        email: user.email,
        username: user.username,
        first_name: user.first_name,
        last_name: user.last_name,
        phone_number: user.phone_number,
        is_active: user.is_active,
        date_joined: user.date_joined,
        profile: user.profile,
      };
      await mockApiService.saveUser(mockUser);
    }
  }

  async saveToken(token: string): Promise<void> {
    if (USE_REAL_API) {
      await realApiService.saveToken(token);
    } else {
      await mockApiService.saveToken(token);
    }
  }

  async getStoredToken(): Promise<string | null> {
    if (USE_REAL_API) {
      return await realApiService.getStoredToken();
    } else {
      return await mockApiService.getStoredToken();
    }
  }

  async getStoredUser(): Promise<AuthUser | null> {
    if (USE_REAL_API) {
      return await realApiService.getStoredUser();
    } else {
      const response = await mockApiService.getCurrentUser();
      return response.success && response.data ? TypeAdapter.mockUserToReal(response.data) : null;
    }
  }

  async clearAuthData(): Promise<void> {
    if (USE_REAL_API) {
      await realApiService.clearAuthData();
    } else {
      await mockApiService.logout();
    }
  }

  // Additional methods for backward compatibility
  async getUserProfile(): Promise<ApiResponse<AuthUser>> {
    return await this.getCurrentUser();
  }

  async updateUserProfile(userData: Partial<AuthUser>): Promise<ApiResponse<{ user: AuthUser }>> {
    const currentUser = await this.getCurrentUser();
    if (!currentUser.success || !currentUser.data) {
      return {
        success: false,
        error: 'No authenticated user'
      };
    }

    const updatedUser = { ...currentUser.data, ...userData };
    await this.saveUser(updatedUser);

    return {
      success: true,
      data: { user: updatedUser }
    };
  }

  async changePassword(data: {
    current_password: string;
    new_password: string;
    new_password_confirm: string;
  }): Promise<ApiResponse<{ message: string }>> {
    // TODO: Implement real password change when backend endpoint is available
    await new Promise(resolve => setTimeout(resolve, 500));
    return {
      success: true,
      data: { message: 'Password changed successfully' }
    };
  }

  async requestPasswordReset(email: string): Promise<ApiResponse<{ message: string }>> {
    // TODO: Implement real password reset when backend endpoint is available
    await new Promise(resolve => setTimeout(resolve, 1000));
    return {
      success: true,
      data: { message: 'Password reset email sent' }
    };
  }

  async confirmPasswordReset(data: {
    token: string;
    new_password: string;
    new_password_confirm: string;
  }): Promise<ApiResponse<{ message: string }>> {
    // TODO: Implement real password reset confirmation when backend endpoint is available
    await new Promise(resolve => setTimeout(resolve, 500));
    return {
      success: true,
      data: { message: 'Password reset successfully' }
    };
  }

  async getUserStatus(): Promise<ApiResponse<AuthUser>> {
    return await this.getCurrentUser();
  }
}

export default new ApiService();
