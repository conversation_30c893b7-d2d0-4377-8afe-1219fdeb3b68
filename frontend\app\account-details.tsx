import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Modal,
  Alert,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { ArrowLeft, Eye, EyeOff, Download, Share, Copy, MoveHorizontal as MoreHorizontal, TrendingUp, Calendar, RefreshCw, AlertTriangle } from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useTheme } from '@/hooks/useTheme';
import { Account, Transaction, DisplayAccount, DisplayTransaction, TypeConverter } from '@/types';
import TransactionItem from '@/components/TransactionItem';
import OfflineIndicator from '@/components/OfflineIndicator';
import ErrorBoundary from '@/components/ErrorBoundary';
import accountDataService from '@/services/accountDataService';
import networkService from '@/services/networkService';
import { pathResolver } from '@/utils/pathResolver';
import { pathErrorHandler } from '@/utils/pathErrorHandler';
import { FileSystem } from 'expo-file-system';
import enhancedErrorService from '@/services/enhancedErrorService';

// Error handler for account details screen
const handleAccountScreenError = (error: Error, errorInfo: React.ErrorInfo) => {
  console.error('Error in AccountDetailsScreen:', error, errorInfo);
  
  // Log the error using pathErrorHandler if it's a file path error
  if (error.message.includes('ENOENT') || error.message.includes('no such file or directory')) {
    pathErrorHandler.handlePathError(error, 'account-details.tsx', {
      component: 'AccountDetailsScreen',
      errorInfo
    });
  }
};

// Main content component wrapped with error boundary
function AccountDetailsContent() {
  const { theme } = useTheme();
  const { id } = useLocalSearchParams();
  const [isBalanceVisible, setIsBalanceVisible] = useState(true);
  const [showActions, setShowActions] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [accountData, setAccountData] = useState<{
    account: Account | null,
    transactions: Transaction[] | null,
    isOffline: boolean,
    lastUpdated: Date | null
  }>({
    account: null,
    transactions: null,
    isOffline: false,
    lastUpdated: null
  });

  // Display data for UI components (converted from API data)
  const [displayData, setDisplayData] = useState<{
    account: DisplayAccount | null,
    transactions: DisplayTransaction[] | null
  }>({
    account: null,
    transactions: null
  });

  // State for error handling
  const [error, setError] = useState<{ hasError: boolean; message: string | null }>({
    hasError: false,
    message: null
  });

  // Validate resources to prevent ENOENT errors
  const validateResources = useCallback(async () => {
    try {
      // For web platform, skip file system validation as it's not supported
      if (Platform.OS === 'web') {
        console.log('Skipping file system validation on web platform');
        return true;
      }
      
      // Use absolute paths with proper error handling
      const appDir = FileSystem.documentDirectory || '';
      
      // Use safer path resolution with try/catch for each path
      try {
        // Instead of validating paths that might not exist in the file system,
        // we'll check if the modules are properly imported
        console.log('Validating critical dependencies...');
        
        // Check if TransactionItem component is available
        if (typeof TransactionItem !== 'function') {
          throw new Error('TransactionItem component is not available');
        }
        
        // Check if OfflineIndicator component is available
        if (typeof OfflineIndicator !== 'function') {
          throw new Error('OfflineIndicator component is not available');
        }
        
        // Check if accountDataService is available
        if (!accountDataService || typeof accountDataService.getAccountById !== 'function') {
          throw new Error('accountDataService is not properly initialized');
        }
        
        // Check if networkService is available
        if (!networkService || typeof networkService.isOnline !== 'function') {
          throw new Error('networkService is not properly initialized');
        }
      } catch (componentError) {
        console.error('Component validation error:', componentError);
        throw componentError;
      }
      
      return true;
    } catch (error) {
      console.error('Error validating resources:', error);
      
      // Don't set error state for web platform validation failures
      if (Platform.OS !== 'web') {
        // Log the error using pathErrorHandler
        pathErrorHandler.handlePathError(error, 'account-details.tsx', {
          method: 'validateResources',
          component: 'AccountDetailsScreen'
        });
        
        setError({
          hasError: true,
          message: error instanceof Error ? error.message : 'Failed to validate resources'
        });
        
        return false;
      }
      
      return true;
    }
  }, []);

  // Fetch account data
  const fetchAccountData = async () => {
    try {
      setIsLoading(true);
      
      // Validate resources first
      const resourcesValid = await validateResources();
      if (!resourcesValid) {
        throw new Error('Failed to validate resources');
      }
      
      // Fetch account details
      const accountResponse = await accountDataService.getAccountById(id as string);
      
      // Fetch transactions for this account
      const transactionsResponse = await accountDataService.getTransactions(id as string);
      
      const account = accountResponse.data?.[0] || null;
      const transactions = transactionsResponse.data || [];

      setAccountData({
        account,
        transactions,
        isOffline: accountResponse.isOffline || transactionsResponse.isOffline,
        lastUpdated: accountResponse.lastUpdated || transactionsResponse.lastUpdated
      });

      // Convert to display format for UI components
      setDisplayData({
        account: account ? TypeConverter.accountToDisplay(account) : null,
        transactions: transactions.map(TypeConverter.transactionToDisplay)
      });

      // Clear any previous errors
      setError({ hasError: false, message: null });
    } catch (error) {
      console.error('Error fetching account data:', error);
      
      // Check if it's a file path error
      if (error instanceof Error && 
          (error.message.includes('ENOENT') || error.message.includes('no such file or directory'))) {
        // Log the error using pathErrorHandler
        pathErrorHandler.handlePathError(error, 'account-details.tsx', {
          method: 'fetchAccountData',
          component: 'AccountDetailsScreen'
        });
        
        setError({
          hasError: true,
          message: `File path error: ${error.message}`
        });
      }

      // Use enhanced error handling
      await enhancedErrorService.handleError(error, {
        showAlert: true,
        retryCallback: fetchAccountData,
        context: { operation: 'fetchAccountData', accountId: id }
      });

      // Set error state
      setError({
        hasError: true,
        message: error instanceof Error ? error.message : 'Failed to load account data'
      });

      setAccountData({
        account: null,
        transactions: null,
        isOffline: !networkService.isOnline(),
        lastUpdated: null
      });

      setDisplayData({
        account: null,
        transactions: null
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Refresh data
  const handleRefresh = async () => {
    if (!networkService.isOnline()) {
      Alert.alert(
        'Offline Mode',
        'You are currently offline. Please check your internet connection and try again.',
        [{ text: 'OK' }]
      );
      return;
    }
    
    setIsRefreshing(true);
    
    // Invalidate caches to force fresh data fetch
    await accountDataService.invalidateAccountCache(id as string);
    await accountDataService.invalidateTransactionCache(id as string);
    
    // Fetch fresh data
    await fetchAccountData();
  };

  // Load data on mount
  useEffect(() => {
    fetchAccountData();
  }, [id]);

  // Get account and transactions from state
  const { isOffline, lastUpdated } = accountData;
  const { account, transactions } = displayData;

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.primary} />
          <Text style={[styles.loadingText, { color: theme.text }]}>Loading account details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Show error state if there's an error
  if (error.hasError) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
        <View style={styles.errorContainer}>
          <AlertTriangle size={48} color={theme.error} style={styles.errorIcon} />
          <Text style={[styles.errorTitle, { color: theme.error }]}>Error Loading Account</Text>
          <Text style={[styles.errorText, { color: theme.text }]}>
            {error.message || 'An unexpected error occurred'}
          </Text>
          <TouchableOpacity 
            style={[styles.retryButton, { backgroundColor: theme.primary }]}
            onPress={() => {
              setError({ hasError: false, message: null });
              fetchAccountData();
            }}
          >
            <RefreshCw size={16} color="#FFFFFF" style={{ marginRight: 8 }} />
            <Text style={styles.retryText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  if (!account) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: theme.text }]}>Account not found</Text>
          <TouchableOpacity 
            style={[styles.backHomeButton, { backgroundColor: theme.primary }]}
            onPress={() => router.back()}
          >
            <Text style={styles.retryText}>Back to Accounts</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const formatAmount = (amount: number) => {
    return isBalanceVisible ? `₹${Math.abs(amount).toLocaleString('en-IN')}` : '₹****';
  };

  const handleCopyAccountNumber = () => {
    Alert.alert('Copied', 'Account number copied to clipboard');
  };

  const handleDownloadStatement = () => {
    Alert.alert(
      'Download Statement',
      'Choose format and date range',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'PDF (Last 3 months)', onPress: () => downloadStatement('pdf', '3months') },
        { text: 'Excel (Last 6 months)', onPress: () => downloadStatement('excel', '6months') },
      ]
    );
  };

  const downloadStatement = (format: string, period: string) => {
    Alert.alert('Success', `${format.toUpperCase()} statement for ${period} will be downloaded shortly`);
  };

  const handleShareStatement = () => {
    Alert.alert(
      'Share Statement',
      'Choose sharing method',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Email', onPress: () => shareStatement('email') },
        { text: 'WhatsApp', onPress: () => shareStatement('whatsapp') },
        { text: 'More Options', onPress: () => shareStatement('more') },
      ]
    );
  };

  const shareStatement = (method: string) => {
    Alert.alert('Success', `Statement shared via ${method}`);
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.background,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      marginTop: 16,
      fontSize: 16,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 20,
      paddingTop: 40,
    },
    offlineContainer: {
      paddingHorizontal: 20,
      marginBottom: 8,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    refreshButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.surfaceVariant,
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
    },
    refreshText: {
      marginLeft: 4,
      fontSize: 12,
      color: theme.primary,
      fontWeight: '500',
    },
    rotating: {
      transform: [{ rotate: '45deg' }],
    },
    backButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: theme.surfaceVariant,
      justifyContent: 'center',
      alignItems: 'center',
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.text,
      flex: 1,
      textAlign: 'center',
      marginHorizontal: 16,
    },
    moreButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: theme.surfaceVariant,
      justifyContent: 'center',
      alignItems: 'center',
    },
    accountCard: {
      backgroundColor: theme.primary,
      marginHorizontal: 20,
      marginBottom: 24,
      padding: 24,
      borderRadius: 16,
      elevation: 4,
      shadowColor: theme.shadowDark,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
    },
    accountType: {
      fontSize: 16,
      color: theme.textOnPrimary,
      opacity: 0.8,
      marginBottom: 8,
    },
    balanceContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 16,
    },
    balance: {
      fontSize: 32,
      fontWeight: 'bold',
      color: theme.textOnPrimary,
    },
    eyeButton: {
      padding: 8,
    },
    accountInfo: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    accountNumber: {
      fontSize: 14,
      color: theme.textOnPrimary,
      opacity: 0.8,
    },
    copyButton: {
      padding: 4,
    },
    quickActions: {
      flexDirection: 'row',
      paddingHorizontal: 20,
      marginBottom: 32,
      gap: 12,
    },
    actionButton: {
      flex: 1,
      backgroundColor: theme.surfaceVariant,
      padding: 16,
      borderRadius: 12,
      alignItems: 'center',
    },
    actionIcon: {
      marginBottom: 8,
    },
    actionText: {
      fontSize: 12,
      color: theme.text,
      fontWeight: '500',
    },
    statsContainer: {
      flexDirection: 'row',
      paddingHorizontal: 20,
      marginBottom: 24,
      gap: 12,
    },
    statCard: {
      flex: 1,
      backgroundColor: theme.surfaceVariant,
      padding: 16,
      borderRadius: 12,
    },
    statLabel: {
      fontSize: 12,
      color: theme.textSecondary,
      marginBottom: 4,
    },
    statValue: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.text,
    },
    section: {
      marginBottom: 24,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.text,
      paddingHorizontal: 20,
      marginBottom: 16,
    },
    transactionsList: {
      backgroundColor: theme.surface,
      borderRadius: 16,
      marginHorizontal: 20,
      overflow: 'hidden',
      elevation: 2,
      shadowColor: theme.shadow,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.22,
      shadowRadius: 2.22,
      borderWidth: 1,
      borderColor: theme.border,
    },
    emptyTransactions: {
      padding: 40,
      alignItems: 'center',
    },
    emptyText: {
      fontSize: 16,
      color: theme.textSecondary,
      textAlign: 'center',
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: theme.overlay,
      justifyContent: 'flex-end',
    },
    modalContent: {
      backgroundColor: theme.background,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      padding: 20,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.text,
      marginBottom: 20,
      textAlign: 'center',
    },
    modalAction: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 16,
      paddingHorizontal: 16,
      borderRadius: 12,
      marginBottom: 8,
    },
    modalActionText: {
      fontSize: 16,
      color: theme.text,
      marginLeft: 16,
      fontWeight: '500',
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    errorIcon: {
      marginBottom: 16,
    },
    errorTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      marginBottom: 8,
      textAlign: 'center',
    },
    errorText: {
      fontSize: 16,
      textAlign: 'center',
      marginBottom: 24,
      color: theme.textSecondary,
    },
    retryButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 8,
    },
    retryText: {
      color: '#FFFFFF',
      fontSize: 16,
      fontWeight: '500',
    },
    backHomeButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 8,
      marginTop: 16,
    },
    errorTransactionsList: {
      backgroundColor: theme.surfaceVariant,
      borderColor: theme.warning,
    },
    smallRetryButton: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 4,
      marginTop: 12,
    },
    smallRetryText: {
      color: '#FFFFFF',
      fontSize: 14,
      fontWeight: '500',
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={theme.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Account Details</Text>
        <TouchableOpacity
          style={styles.moreButton}
          onPress={() => setShowActions(true)}
        >
          <MoreHorizontal size={24} color={theme.text} />
        </TouchableOpacity>
      </View>
      
      {/* Offline Indicator */}
      {isOffline && (
        <View style={styles.offlineContainer}>
          <OfflineIndicator lastUpdated={lastUpdated} />
          <TouchableOpacity 
            style={styles.refreshButton}
            onPress={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw size={16} color={theme.primary} style={isRefreshing ? styles.rotating : undefined} />
            <Text style={styles.refreshText}>{isRefreshing ? 'Refreshing...' : 'Refresh'}</Text>
          </TouchableOpacity>
        </View>
      )}

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Account Card */}
        <View style={styles.accountCard}>
          <Text style={styles.accountType}>{account.type} Account</Text>
          <View style={styles.balanceContainer}>
            <Text style={styles.balance}>{formatAmount(account.balance)}</Text>
            <TouchableOpacity
              style={styles.eyeButton}
              onPress={() => setIsBalanceVisible(!isBalanceVisible)}
            >
              {isBalanceVisible ? (
                <Eye size={24} color={theme.textOnPrimary} />
              ) : (
                <EyeOff size={24} color={theme.textOnPrimary} />
              )}
            </TouchableOpacity>
          </View>
          <View style={styles.accountInfo}>
            <Text style={styles.accountNumber}>Account: {account.accountNumber}</Text>
            <TouchableOpacity style={styles.copyButton} onPress={handleCopyAccountNumber}>
              <Copy size={16} color={theme.textOnPrimary} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <TouchableOpacity style={styles.actionButton} onPress={() => router.push(`/transfer?sourceAccount=${account.id}`)}>
            <TrendingUp size={24} color={theme.primary} style={styles.actionIcon} />
            <Text style={styles.actionText}>Transfer Money</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={handleDownloadStatement}>
            <Download size={24} color={theme.primary} style={styles.actionIcon} />
            <Text style={styles.actionText}>Statement</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={handleShareStatement}>
            <Share size={24} color={theme.primary} style={styles.actionIcon} />
            <Text style={styles.actionText}>Share Statement</Text>
          </TouchableOpacity>
        </View>

        {/* Account Statistics */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statLabel}>Interest Rate</Text>
            <Text style={styles.statValue}>{account.interestRate}% p.a.</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statLabel}>Accrued Interest</Text>
            <Text style={styles.statValue}>₹{Math.abs(account.accruedInterest).toLocaleString('en-IN')}</Text>
          </View>
        </View>

        {/* Recent Transactions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Transactions</Text>
          {transactions && transactions.length > 0 ? (
            <ErrorBoundary
              componentName="TransactionsList"
              fallback={
                <View style={[styles.transactionsList, styles.errorTransactionsList]}>
                  <View style={styles.emptyTransactions}>
                    <AlertTriangle size={24} color={theme.warning} style={{ marginBottom: 8 }} />
                    <Text style={styles.errorText}>Failed to load transactions</Text>
                    <TouchableOpacity 
                      style={[styles.smallRetryButton, { backgroundColor: theme.primary }]}
                      onPress={handleRefresh}
                    >
                      <Text style={styles.smallRetryText}>Retry</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              }
            >
              <View style={styles.transactionsList}>
                {transactions.slice(0, 5).map((transaction) => (
                  <TransactionItem key={transaction.id} transaction={transaction} />
                ))}
              </View>
            </ErrorBoundary>
          ) : (
            <View style={styles.emptyTransactions}>
              <Text style={styles.emptyText}>No transactions found for this account</Text>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Actions Modal */}
      <Modal
        visible={showActions}
        transparent
        animationType="slide"
        onRequestClose={() => setShowActions(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowActions(false)}
        >
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Account Actions</Text>
            
            <TouchableOpacity style={styles.modalAction} onPress={handleDownloadStatement}>
              <Download size={20} color={theme.primary} />
              <Text style={styles.modalActionText}>Download Statement</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.modalAction} onPress={handleShareStatement}>
              <Share size={20} color={theme.primary} />
              <Text style={styles.modalActionText}>Share Account Details</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.modalAction}>
              <Calendar size={20} color={theme.primary} />
              <Text style={styles.modalActionText}>Set Alerts</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </SafeAreaView>
  );
}

// Export the component wrapped in an ErrorBoundary
export default function AccountDetailsScreen() {
  return (
    <ErrorBoundary 
      componentName="AccountDetailsScreen"
      onError={handleAccountScreenError}
    >
      <AccountDetailsContent />
    </ErrorBoundary>
  );
}